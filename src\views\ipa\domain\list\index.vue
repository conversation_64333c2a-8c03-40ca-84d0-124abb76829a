<template>
    <ct-section-wrap>
        <template #header>
            <div class="title-wrap">
                <div class="title">
                    <span class="text">IP应用加速接入</span>
                    <span class="info">展示已启用和已停用的域名/实例。</span>
                </div>
            </div>
        </template>

        <ct-box class="table-scroll-wrap">
            <!-- 搜索条 -->
            <div class="flex-row-style search-box">
                <div class="flex-row-style">
                    <el-button type="primary" class="add-btn" icon="el-icon-plus" @click="goDomainAdd"
                        >新增接入</el-button
                    >
                </div>
                <div class="search-bar">
                    <!-- 接入方式 下拉框 -->
                    <el-select v-model="query.access_mode" placeholder="请选择" @change="access_mode_change">
                        <el-option label="所有接入方式" :value="0"></el-option>
                        <el-option label="域名接入" :value="1"></el-option>
                        <el-option label="无域名接入" :value="2"></el-option>
                    </el-select>
                    <!-- 状态 -->
                    <el-select v-model="status" placeholder="请选择">
                        <el-option
                            v-for="(item, index) in statusList"
                            :key="index"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                    <el-input
                        v-if="query.access_mode !== 2"
                        class="btn"
                        placeholder="请输入域名关键字"
                        v-model.lazy="query.domain"
                        clearable
                    />
                    <el-input
                        v-if="query.access_mode === 2"
                        class="btn"
                        placeholder="请输入实例名称关键字"
                        v-model.lazy="query.inst_name"
                        clearable
                    />
                    <el-button type="primary" @click="handleSearch" class="btn">查询</el-button>
                    <el-button @click="reset" class="btn">重置</el-button>
                    <el-button icon="el-icon-refresh" @click="handleSearch"></el-button>
                </div>
            </div>
            <!-- 表格 -->
            <el-table :data="tableList" v-loading="tableLoading">
                <!-- @sort-change="onSortChange" -->
                <el-table-column type="index" :index="indexMethod" label="序号" width="66"> </el-table-column>
                <el-table-column prop="domain" label="域名" minWidth="120" align="left"> </el-table-column>
                <el-table-column prop="inst_name" label="实例名称" minWidth="100" align="left">
                    <template slot-scope="{ row }">
                        {{ row.inst_name }}
                        <i
                            class="el-icon-edit edit-style"
                            v-if="row.access_mode === 2"
                            @click="handleUpdateInstName(row)"
                        ></i>
                    </template>
                </el-table-column>
                <el-table-column prop="access_mode" label="接入方式" minWidth="80" align="left">
                    <template slot-scope="scope">
                        {{ access_mode_label(scope.row.access_mode) }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="isJinhua"
                    label="高防CNAME"
                    minWidth="120"
                    align="left"
                    prop="defenseCname"
                />
                <el-table-column v-else label="CNAME" minWidth="120" align="left" prop="cname" />
                <el-table-column prop="area_scope" label="加速区域" minWidth="80" align="left">
                    <template slot-scope="scope">
                        {{ areaScopeLabel(scope.row.area_scope) }}
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" minWidth="80" align="left">
                    <template slot-scope="{ row }">
                        <div class="status-panel">
                            <span :class="`dot dot-${row.status}`"></span>
                            <span>
                                {{ statusLabel(row.status, row.creating) }}
                            </span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="isJinhua"
                    label="高防IP"
                    minWidth="120"
                    align="left"
                    prop="defenseIp"
                />
                <el-table-column v-if="!isJinhua" label="IPv6解析" align="left">
                    <template slot-scope="scope">
                        <span class="ipv6-table-item-style">
                            <el-switch
                                v-if="!(scope.row.status === 3 && [1, 2].includes(scope.row.ipv6_status))"
                                :value="scope.row.ipv6_enable"
                                :disabled="!isDomainAvailable(scope.row.status)"
                                @change="ipv6_enable_change(scope.row)"
                            ></el-switch>
                            <!-- IPv6解析-图标展示 -->
                            <el-icon
                                v-show="getIpv6Icon(scope.row)"
                                class="icon-style"
                                :class="getIpv6Icon(scope.row)"
                            ></el-icon>
                            <!-- IPv6解析-文字展示 -->
                            <span style="margin-left:4px">{{ getIpv6Text(scope.row) }}</span>
                        </span>
                    </template>
                </el-table-column>
                <!-- DDoS防护 -->
                <el-table-column v-if="!isJinhua" label="DDoS防护" minWidth="50" align="left">
                    <template slot-scope="scope">
                        <el-switch
                            v-if="scope.row.area_scope === 1"
                            :value="scope.row.ddos_switch"
                            active-value="ON"
                            inactive-value="CLOSE"
                            :disabled="!hasDdos || scope.row.status !== 4"
                            @change="ddos_switch_change(scope.row)"
                        ></el-switch>
                        <span v-else style="text-align:center">{{
                            "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-"
                        }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="210" align="left">
                    <template slot-scope="scope">
                        <el-button
                            class="operator-text"
                            type="text"
                            @click="lookOrEdit(scope.row, 'domain.edit')"
                            >加速配置</el-button
                        >
                        <el-button class="operator-text" type="text" @click="gotoDDos">DDoS防护</el-button>
                        <el-button
                            class="operator-text"
                            type="text"
                            size="small"
                            @click="domainOperate(scope.row, 2)"
                            v-if="scope.row.status === 6"
                            :loading="scope.row.isLoading"
                            :disabled="isStatusDisabled(scope.row)"
                            >启用</el-button
                        >
                        <!-- 4.已启用 -->
                        <el-button
                            type="text"
                            size="small"
                            @click="domainOperate(scope.row, 1)"
                            v-if="scope.row.status === 4 || scope.row.status !== 6"
                            :loading="scope.row.isLoading"
                            :disabled="isStatusDisabled(scope.row)"
                            >停用</el-button
                        >
                        <el-button
                            type="text"
                            @click="del(scope.row, 'deleteLoading')"
                            :loading="scope.row.deleteLoading"
                            :disabled="isDeleteDisabled(scope.row)"
                            >删除</el-button
                        >
                        <el-button
                            type="text"
                            :disabled="isScopeChangeDisabled(scope.row)"
                            @click="handleAcceAreaChange(scope.row)"
                        >
                            区域变更
                        </el-button>
                        <!-- </template>
                        </ct-select> -->
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页器 -->
            <el-pagination
                :layout="'total, sizes, prev, pager, next'"
                :total="filterList.length"
                :current-page.sync="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :hide-on-single-page="false"
                @current-change="pageChange"
                @size-change="sizeChange"
            />
        </ct-box>
        <el-dialog
            title="修改实例名称"
            :append-to-body="true"
            :close-on-click-modal="false"
            :visible.sync="dialogVisible"
            :before-close="cancel"
            width="500px"
            :show-close="true"
        >
            <el-form :model="dialogForm" ref="dialogForm" label-width="80px">
                <el-form-item label="实例名称" prop="inst_name" :rules="rules.inst_name">
                    <el-input
                        v-model.trim="dialogForm.inst_name"
                        placeholder="请输入不超过10个字符的中/英文/数字实例名称"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" class="btns">
                <el-button :loading="tableLoading" @click="cancel">取消</el-button>
                <el-button type="primary" :loading="tableLoading" @click="handleSubmit">确定</el-button>
            </div>
        </el-dialog>
        <!-- 变更区域弹窗-->
        <change-acce-area ref="changeAcceArea" @success="search" />
    </ct-section-wrap>
</template>
<script>
import { DomainUrl } from "@/config/url/ipa/domain";
import { cloneDeep } from "lodash-es";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import commonMixin from "@/views/ipa/domain/commonMixin";
import { CODE_OK } from "@/utils/ctFetch/errorConfig";
import changeAcceArea from "@/views/ipa/domain/components/changeAcceArea/index.vue";

export default {
    mixins: [commonMixin],
    components: { changeAcceArea },
    data() {
        return {
            // 搜索条
            query: {
                account_id: this.$store.state.user.userInfo.userId,
                product_code: "",
                // status: "",
                domain: "",
                inst_name: "",
                access_mode: 0,
            },
            status: "",
            statusList: [
                {
                    label: "所有状态",
                    value: "",
                },
                {
                    label: "已启用",
                    value: 4,
                },
                {
                    label: "配置中",
                    value: 3,
                },
                {
                    label: "已停止",
                    value: 6,
                },
            ],
            // 分页器
            page: 1,
            pageSize: 10,
            total: 0,
            // 表格
            tableLoading: false,
            // initData: [], //初始得到数据
            dataList: [],
            // formatList: [],
            timer: null,
            dialogVisible: false, // 修改实例名称弹窗
            dialogForm: {
                domain: "",
                inst_name: "",
            },
            rules: {
                inst_name: [
                    { required: true, message: "请输入实例名称", trigger: "blur" },
                    {
                        trigger: ["change", "blur"],
                        pattern: /^[a-zA-Z|\u4E00-\u9FA5|0-9]*$/g,
                        message: "不超过10个字符的中英文+数字",
                    },
                    {
                        max: 10,
                        message: "不超过10个字符的中英文+数字",
                        trigger: ["blur", "change"],
                    },
                ],
            },
        };
    },
    watch: {
        "query.access_mode": {
            handler() {
                this.page = 1;
            },
        },
        "query.domain": {
            handler() {
                this.page = 1;
            },
        },
        "query.inst_name": {
            handler() {
                this.page = 1;
            },
        },
        status() {
            this.page = 1;
        },
    },
    computed: {
        isJinhua() {
            // TODO 0619
            return SecurityAbilityModule.serviceIdentifier === "jinhua";
        },
        /**
         * 过滤表格展示的数据
         */
        filterList() {
            let filterList = this.dataList.slice();
            // 排序：配置中的排在最前面，其他按照时间降序
            filterList.sort((a, b) => {
                if (a.status !== b.status) {
                    if (a.status === 3) {
                        return -1;
                    }
                    if (b.status === 3) {
                        return 1;
                    }
                }
                return new Date(b.insert_date) - new Date(a.insert_date);
            });

            if (this.status) {
                filterList = filterList.filter(item => item.status === this.status);
            }

            // 接入方式
            if (this.query?.access_mode) {
                filterList = filterList.filter(item => item.access_mode === this.query.access_mode);
                const key = this.query.access_mode === 2 ? "inst_name" : "domain";
                if (this.query?.[key]) {
                    filterList = filterList.filter(item => item[key].includes(this.query[key]));
                }
            } else {
                if (this.query?.domain) {
                    filterList = filterList.filter(item => item.domain.includes(this.query.domain));
                }
            }

            return filterList;
        },
        tableList() {
            return this.filterList.slice((this.page - 1) * this.pageSize, this.page * this.pageSize);
        },
        domainOrderStatus() {
            return SecurityAbilityModule.domainOrderStatus;
        },
        hasDdos() {
            return SecurityAbilityModule.hasDdos;
        },
    },
    created() {
        // this.getDomainOrderCheck();
    },
    mounted() {
        this.search();
    },
    beforeDestroy() {
        // if (this.timer) {
        //     clearInterval(this.timer);
        // }
    },
    methods: {
        // 域名是否启用
        isDomainAvailable(status) {
            return status === 4;
        },
        // 点击提交
        async handleSubmit() {
            await this.$refs.dialogForm.validate();
            const param = {
                ...this.dialogForm,
            };
            try {
                this.tableLoading = true;
                await this.updateInstName(param);
                await this.search();
                this.$message.success("修改实例名称成功");
            } catch (e) {
                this.tableLoading = false;
                this.$errorHandler(e);
            } finally {
                this.tableLoading = false;
            }
            this.dialogVisible = false;
        },
        cancel() {
            this.dialogVisible = false;
        },
        access_mode_change() {
            this.$set(this.query, "domain", "");
            this.$set(this.query, "inst_name", "");
        },
        handleUpdateInstName(data) {
            this.dialogForm.domain = data.domain;
            this.dialogForm.inst_name = data.inst_name;
            this.dialogVisible = true;
        },
        // 修改实例名称接口
        updateInstName(data) {
            return this.$ctFetch(DomainUrl.updataInstName, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },
        reqUpdateDomain(data) {
            return this.$ctFetch(DomainUrl.updataDomain, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },
        async ipv6_enable_change(row) {
            let message = "关闭后将不支持IPv6解析，请确认是否关闭？";
            if (!row.ipv6_enable) {
                message = "请确认是否需要支持IPv6解析？";
            }
            await this.$confirm(message, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                beforeClose: async (action, instance, done) => {
                    if (action !== "confirm") {
                        done();
                        return;
                    } else {
                        try {
                            const params = {
                                domain: row.domain,
                                action: 5,
                                ipv6_enable: !row.ipv6_enable,
                            };
                            instance.confirmButtonLoading = true;
                            const res = await this.reqUpdateDomain(params);
                            instance.confirmButtonLoading = false;
                            if (res) {
                                row.ipv6_enable
                                    ? this.$message.success("IPv6解析开关下发成功，等待一段时间之后生效")
                                    : this.$message.success("IPv6解析开关下发成功，等待一段时间之后生效");
                                this.search();
                            }
                        } catch (e) {
                            this.$errorHandler(e);
                            instance.confirmButtonLoading = false;
                            done();
                            return;
                        } finally {
                            instance.confirmButtonLoading = false;
                        }

                        done();
                        return;
                    }
                },
            });
            // }
        },
        getDomainDdos(data) {
            return this.$ctFetch(DomainUrl.domainDdos, {
                method: "POST",
                transferType: "json",
                data: data,
            });
        },
        // async getDomainOrderCheck() {
        //     const resp = await this.$ctFetch(OverviewUrl.domainBillingCheck, {
        //         method: "GET",
        //         transferType: "json",
        //     });
        //     this.domainOrderStatus = resp.result.has_product;
        //     DomainModule.setDomainOrderStatus(resp.result.status);
        // },
        searchKey() {
            this.search();
            this.page = 1;
        },
        reset() {
            this.query = {
                account_id: this.$store.state.user.userInfo.userId,
                // status: "",
                product_code: "",
                domain: "",
                inst_name: "",
                access_mode: 0,
            };
            this.status = "";
            this.page = 1;
            this.search();
        },
        // 分页器
        pageChange(val) {
            this.page = val;
            // this.search();
        },
        sizeChange(val) {
            // 恢复到第一页
            this.page = 1;
            this.pageSize = val;
            // this.search();
        },
        indexMethod(index) {
            // 返回每页的序号
            return this.page * this.pageSize - (this.pageSize - index - 1);
        },
        /**
         * 点击搜索按钮
         */
        handleSearch() {
            this.page = 1;
            this.search();
        },

        // 表格？获取数据 page pageSize
        async search() {
            this.tableLoading = true;
            const rst = await this.getDomainList();
            // ***分页由后端实现
            // // 分页
            // this.initData = result;
            // this.dataList = this.initData.filter(
            //     (item, index) => index < this.page * this.pageSize && index >= this.pageSize * (this.page - 1)
            // );
            const ary = rst.result.map(item => {
                const obj = {
                    ...item,
                    isLoading: false,
                };
                // 删除情况
                if (item.status === 6) {
                    obj.deleteLoading = false;
                }
                return obj;
            });
            this.dataList = ary;
            this.total = rst.total;
            this.tableLoading = false;
        },
        getDomainList() {
            // 域名下拉框改为从工单返回的列表拿
            // return this.$ctFetch(DomainUrl.domainList, {
            return this.$ctFetch(DomainUrl.domainListPage, {
                method: "GET",
                transferType: "json",
            });
        },

        areaScopeLabel(area_scope) {
            let name = "";
            switch (area_scope) {
                case 1:
                    name = "中国内地";
                    break;
                case 2:
                    name = "全球（不含中国内地）";
                    break;
                case 3:
                    name = "全球";
                    break;
                default:
                    name = "中国内地";
                    break;
            }
            return name;
        },

        // 表格？显示？状态过滤
        statusLabel(status, isCreating = false) {
            let label = "";
            switch (status) {
                case 1:
                    label = "审核中";
                    break;
                case 2:
                    label = "审核成功";
                    break;
                case 3:
                    label = `配置中${isCreating ? "(新增)" : "(更新)"}`;
                    break;
                case 4:
                    label = "已启用";
                    break;
                case 5:
                    label = "停止中";
                    break;
                case 6:
                    label = "已停止";
                    break;
                case 7:
                    label = "删除中";
                    break;
                case 8:
                    label = "已删除";
                    break;
                case 9:
                    label = "审核失败";
                    break;
                case 10:
                    label = "配置失败";
                    break;
                case 11:
                    label = "停止失败";
                    break;
                case 12:
                    label = "删除失败";
                    break;
            }
            return label;
        },
        // ***表格？显示？字体颜色
        statusLabelColor(status) {
            return status === 4 ? "rgb(152,205,85)" : "rgb(151,156,159)";
        },

        // 表格？操作？新增
        goDomainAdd() {
            if (!this.domainOrderStatus) {
                this.$alert("您尚未开通边缘接入产品，请完成订购后尝试新增域名。", "提示", {
                    confirmButtonText: "确定",
                });
                return;
            }
            this.$router.push({ name: "domain.create" });
        },
        // 表格？操作？查看+编辑
        lookOrEdit(item, name) {
            this.$router.push({
                name: name,
                query: {
                    domain: item.domain,
                    status: item.status,
                },
            });
        },
        // 表格？操作？删除校验
        del(row, typeLoading) {
            this.$prompt(`危险操作，请输入要删除的域名：${row.domain}`, "删除确认", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                inputErrorMessage: "请输入正确的域名",
                inputValidator: val => val === row.domain,
            }).then(() => {
                this.handleDelete(row, 3, typeLoading);
            });
        },
        gotoDDos() {
            console.log("TODO");
        },
        // 3.删除
        async handleDelete(data, action, typeLoading) {
            data[typeLoading] = true;
            const param = {
                action,
                account_id: this.$store.state.user.userInfo.userId,
                domain_status: data.status,
                domain: data.domain,
                area_scope: data.area_scope,
            };
            try {
                await this.reqDomainOperate(param);
                this.$message.success(`您删除域名的请求已发出`);
                this.search();
            } catch (e) {
                this.$errorHandler(e);
            }
            data[typeLoading] = false;
        },
        isStatusDisabled(row) {
            const status = row.status;
            if (status === 3) {
                return true;
            }

            return false;
        },
        isDeleteDisabled(row) {
            const status = row.status;
            if (status === 6) {
                return false;
            }

            return true;
        },
        // 变更区域按钮是否可操作
        isScopeChangeDisabled(row) {
            const status = row.status;
            return status !== 4;
        },
        // 表格？操作？1.停用2.启用3.删除
        async domainOperate(data, action) {
            const status = data.status;
            let msg = "";
            let title = "";
            data.isLoading = true;
            const param = {
                action,
                account_id: this.$store.state.user.userInfo.userId,
                domain_status: data.status,
                domain: data.domain,
                area_scope: data.area_scope,
            };
            if (status === 6) {
                title = "启用确认";
                msg = "请再次确认是否需要启用该域名？该操作请慎重。";
            } else {
                title = "停用确认";
                msg =
                    "停用加速域名前，请确保已将域名DNS解析记录由原来CNAME至天翼云，调整为解析回源或CNAME指向非天翼云地址。避免因为域名停用导致业务中断！";
            }
            this.$confirm(msg, title, {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                beforeClose: (action, instance, done) => {
                    if (action !== "confirm") {
                        done();
                        return;
                    }
                    this.updateDomainStatus(param, instance, done);
                },
            });
            data.isLoading = false;
        },
        async updateDomainStatus(params, instance, done) {
            instance.confirmButtonLoading = true;
            try {
                await this.reqDomainOperate(params);
                params.action === 1
                    ? this.$message.success("域名停用下发成功，等待一段时间之后生效")
                    : this.$message.success("域名启用下发成功，等待一段时间之后生效");
                this.search();
                done();
            } catch (e) {
                this.$errorHandler(e);
            }
            instance.confirmButtonLoading = false;
        },
        reqDomainOperate(data) {
            return this.$ctFetch(DomainUrl.domainOperate, {
                method: "POST",
                transferType: "json",
                body: {
                    data,
                },
            });
        },
        async ddos_switch_change(row) {
            let message = "请确认是否关闭DDoS防护开关？";
            if (row.ddos_switch === "CLOSE" || row.ddos_switch === "" || row.ddos_switch === undefined) {
                message = "请确认是否打开DDoS防护开关？";
            }
            await this.$confirm(message, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                dangerouslyUseHTMLString: true,
                type: "warning",
            });
            const tempRow = cloneDeep(row);
            let ddos_switch = "";
            tempRow.ddos_switch === "CLOSE" || tempRow.ddos_switch === "" || tempRow.ddos_switch === undefined
                ? (ddos_switch = "ON")
                : tempRow.ddos_switch === "ON"
                ? (ddos_switch = "CLOSE")
                : "";
            // if (ddos_switch === "") return;
            try {
                const rst = await this.getDomainDdos({
                    domain: row.domain,
                    ddos_switch: ddos_switch,
                });

                if (rst.code === CODE_OK && tempRow.ddos_switch === "CLOSE") {
                    row.ddos_switch = "ON";
                } else if (rst.code === CODE_OK && tempRow.ddos_switch === "ON") {
                    row.ddos_switch = "CLOSE";
                }
                this.$message.success("配置下发成功，等待一段时间之后生效。");
                this.search();
            } catch (error) {
                let message = error?.data?.reason || error?.data?.message;
                if (error?.data?.inner_err) message += `（${error?.data?.inner_err}）`;
                await this.$alert(message, "提示", {
                    type: "warning",
                });
                return false;
            }
        },
        /**
         * 获取ipv6-文字
         */
        getIpv6Text(row) {
            let text = "";
            const status = row.status;
            const ipv6_enable = row.ipv6_enable;
            const ipv6_status = row.ipv6_status;
            if (
                (status !== 3 && ipv6_enable && ![1, 2].includes(ipv6_status)) ||
                (status === 3 && ipv6_status === 0 && ipv6_enable)
            ) {
                text = "已开启";
            } else if (
                (status !== 3 && !ipv6_enable && ![1, 2].includes(ipv6_status)) ||
                (status === 3 && ipv6_status === 0 && !ipv6_enable)
            ) {
                text = "已关闭";
            } else if (status === 3 && ipv6_status === 1) {
                text = "开启中";
            } else if (status === 3 && ipv6_status === 2) {
                text = "关闭中";
            }
            return text;
        },
        /**
         * 获取ipv6-icon样式
         */
        getIpv6Icon(row) {
            let icon = "";
            const status = row.status;
            const ipv6_enable = row.ipv6_enable;
            const ipv6_status = row.ipv6_status;
            if (
                (status !== 3 && ipv6_enable && ![1, 2].includes(ipv6_status)) ||
                (status === 3 && ipv6_status === 0 && ipv6_enable)
            ) {
                icon = "el-icon-circle-check open ml-4";
            } else if (
                (status !== 3 && !ipv6_enable && ![1, 2].includes(ipv6_status)) ||
                (status === 3 && ipv6_status === 0 && !ipv6_enable)
            ) {
                icon = "el-icon-circle-close gray ml-4";
            } else if (status === 3 && ipv6_status === 1) {
                icon = "el-icon-loading open";
            } else if (status === 3 && ipv6_status === 2) {
                icon = "el-icon-loading close";
            }
            return icon;
        },
        /**
         * 处理加速区域变更
         */
        handleAcceAreaChange(row) {
            if (!this.domainOrderStatus) {
                this.$alert("您尚未开通边缘接入产品，请完成订购后尝试区域变更。", "提示", {
                    confirmButtonText: "确定",
                });
                return;
            }

            this.$refs.changeAcceArea.open(row);
        },
    },
    name: "list",
};
</script>

<style lang="scss" scoped>
.search-panel {
    display: flex;
    margin-bottom: 12px;
    .el-input {
        width: 200px;
        margin: 0 15px;
    }
}
.status-panel {
    display: flex;
    align-items: center;
    .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: $color-danger;
        margin-right: 5px;
    }
    .dot-1,
    .dot-2,
    .dot-3,
    .dot-5 {
        background: $theme-color;
    }
    .dot-4 {
        background: $color-success;
    }
    .dot-6 {
        background: #999999;
    }
}
.ipv6-table-item-style {
    display: flex;
    align-items: center;
    .icon-style {
        // margin-left: $margin;
        font-size: $text-size-lg;
    }
    .open {
        color: $color-success !important;
    }
    .close {
        color: $color-neutral-9 !important;
    }
    .gray {
        color: $color-neutral-6 !important;
    }
    .ml-4 {
        margin-left: $margin;
    }
}
.operator-text {
    color: $theme-color;
}
.el-pagination {
    margin-top: 20px;
}

/* switch按钮样式 */
.switch {
    ::v-deep {
        .el-switch__label {
            position: absolute;
            display: none;
            color: $color-white !important;
            font-size: 12px;
            width: 48px !important;
        }
        .el-switch__label--right {
            z-index: 1;
        }
        .el-switch__label--right span {
            margin-right: 10px;
        }
        .el-switch__label--left {
            z-index: 1;
        }
        .el-switch__label--left span {
            margin-left: 20px;
        }
        .el-switch__label.is-active {
            display: block;
        }
    }
}
/* 调整按钮的宽度 */
.switch.el-switch {
    ::v-deep {
        .el-switch__core {
            width: 48px !important;
            margin: 0;
        }
    }
}

.ct-select__dropdown .el-button {
    display: block;
    margin: 0 20px;
    color: $color-neutral-7;
}
.title-wrap {
    width: 100%;
    height: 56px;
    background-color: $color-white;
    padding: 20px;
    position: relative;
    .title {
        display: flex;
        align-items: center;
        .icon {
            width: 6px;
            height: 20px;
            margin-right: 20px;
            background-color: $color-danger;
        }
        .text {
            font-size: 14px;
            font-weight: 400;
            color: $color-neutral-10 !important;
            font-weight: 700;
            // font-family: PingFangSC-Regular;
        }
        .info {
            color: $text-color-light !important;
            margin-left: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 12px !important;
        }
        .status {
            display: flex;
            align-items: center;
            .dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: $color-black;
                margin: 0 12px 0 32px;
            }
            .status-text {
                color: green;
            }
        }
    }
    .tip {
        height: 30px;
        display: flex;
        align-items: center;
        padding-right: 120px;
        box-sizing: border-box;
        .info {
            color: $color-neutral-7;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .lookFor {
            color: $color-danger;
            cursor: pointer;
            margin-left: 10px;
            width: 120px;
            text-align: center;
        }
    }
    .add-btn {
        position: absolute;
        right: 30px;
        top: 30px;
    }
}
.el-icon-warning-outline {
    color: $color-danger;
    margin-left: 5px;
}
.flex-row-style {
    display: flex;
    flex-direction: row;
    align-items: center;
}
.search-box {
    justify-content: space-between;
}
// .search-bar-style {
//     display: flex;
//     justify-content: flex-end;
// }
.btn {
    margin-left: 8px !important;
}
.edit-style {
    cursor: pointer;
}
</style>
