<template>
    <el-dialog
        custom-class="aocdn-check-dialog"
        title=""
        width="1000px"
        :visible.sync="visible"
        :close-on-click-modal="false"
    >
        <div>
            <ct-svg-icon icon-class="info-circle" class-name="alert-icon"></ct-svg-icon>
            域名【{{ domain }}】需要完成归属权验证，您可以通过DNS解析验证或文件验证，若操作失败请
            <a :href="orderLink" type="warning" class="validate-a" target="_blank"> 前往客服工单系统</a
            >提客户工单。
        </div>
        <el-tabs v-model="activeName" @tab-click="resetVerifyResult">
            <el-tab-pane label="DNS解析验证" name="first">
                <div>
                    1、请在您的域名DNS服务商添加以下TXT记录。<a
                        class="validate-a aocdn-ignore-link"
                        @click="$docHelp(verifyOperationGuideOrder)"
                        >【验证操作指南】</a
                    >
                </div>
                <el-table :data="tableData" border style="width: 100%; margin-top: 15px">
                    <el-table-column prop="zone_type" label="记录类型" width="200" align="center">
                    </el-table-column>
                    <el-table-column prop="zone_host" label="主机记录" width="200" align="center">
                    </el-table-column>
                    <el-table-column prop="zone_record" label="记录值" align="center"> </el-table-column>
                </el-table>
                <div class="verify-tip">2、等待TXT解析生成。</div>
                <div class="verify-tip">3、单击下方按钮进行验证。</div>
                <el-button type="primary" @click="getVerify(1)">验 证</el-button>
            </el-tab-pane>
            <el-tab-pane label="文件验证" name="second">
                <div>
                    1、请在您的{{ domain_zone }}域名源站根目录创建一个文件。<a
                        class="validate-a aocdn-ignore-link"
                        @click="$docHelp(verifyOperationGuideOrder)"
                        >【验证操作指南】</a
                    >
                </div>
                <div class="verify-tip">文件名：dnsverify.txt，文件内容：{{ zone_record }}</div>
                <div class="verify-tip">2、通过http://{{ domain_zone }}/dnsverify.txt访问到该文件。</div>
                <div class="verify-tip">3、单击下方按钮进行验证。</div>
                <el-button type="primary" @click="getVerify(2)">验 证</el-button>
            </el-tab-pane>
            <div class="verify-tip" v-loading="verifyLoading">
                验证结果：
                <span class="verify-rst">
                    {{
                        verify_result === false
                            ? `验证失败（失败原因：${verify_result_desc}）`
                            : verify_result === true
                            ? "验证成功，您可以继续添加域名"
                            : "待验证"
                    }}
                </span>
            </div>
        </el-tabs>
        <div slot="footer">
            <el-button @click="closeDialog" plain>关 闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";
import urlTransformer from "@/utils/logic/url";
import { DomainUrl } from "@/config/url/ipa/domain";

export default {
    name: "IpaDomainVerifyDialog",
    components: { ctSvgIcon },
    props: {
        visible: Boolean,
        domain: String,
        checkResult: Object,
    },
    data() {
        return {
            activeName: "first",
            verify_result: "",
            verify_result_desc: "",
            verifyLoading: false, // 校验过程loading
            tableData: [],
            domain_zone: "",
            zone_record: "",
        };
    },
    computed: {
        orderLink() {
            return urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/h5/wsc/worksheet/submit",
                a1Ctcloud: "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn",
            });
        },
        // 验证操作指南的链接
        verifyOperationGuideOrder() {
            return urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10326434",
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20695878",
            });
        },
    },
    methods: {
        resetVerifyResult() {
            this.verify_result = "";
            this.verifyLoading = false;
        },
        async getVerify(param) {
            this.verifyLoading = true;
            await this.getZoneVerify(param)
                .then(result => {
                    if (!!result && result.result && this.verifyLoading) {
                        this.verify_result = result.result.verify_result;
                        this.verify_result_desc = result.result.verify_result_desc;
                    }
                })
                .catch(err => {
                    if (!err.data.result && this.verifyLoading) {
                        this.verify_result = false;
                        this.verify_result_desc = err.data.reason;
                    }
                })
                .finally(() => {
                    this.verifyLoading = false;
                });
        },
        getZoneVerify(param) {
            return this.$ctFetch(DomainUrl.domainZoneVerify, {
                method: "POST",
                transferType: "json",
                data: {
                    domain: this.domain,
                    domain_zone: this.domain_zone,
                    verify_type: param,
                },
            });
        },
        closeDialog() {
            this.tableData = [];
            this.$emit("close");
        },
    },
    watch: {
        visible(val) {
            if (!val) return;
            this.domain_zone = this.checkResult.domain_zone;
            this.zone_record = this.checkResult.zone_record;
            this.tableData.push({
                zone_type: this.checkResult.zone_type,
                zone_host: this.checkResult.zone_host,
                zone_record: this.checkResult.zone_record,
            });
        },
        verify_result(val) {
            this.$emit("verifyResult", val);
        },
    },
};
</script>
<style lang="scss">
.aocdn-check-dialog {
    .el-dialog__header {
        display: none !important;
    }
}
</style>
<style lang="scss" scoped>
.validate-a {
    color: $color-master;
    text-decoration: underline;
    font-size: 12px;
    &:hover {
        color: $color-master-hover;
    }
}
.verify-tip {
    line-height: 32px;
}
.verify-rst {
    color: $color-danger;
}
</style>
