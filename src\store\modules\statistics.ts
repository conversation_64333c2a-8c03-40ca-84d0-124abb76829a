import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "../index";
import { ctFetch } from "../../utils";
// import { StatisticsParamsUrl } from "../../config/url";
import { BasicUrl } from "@/config/url/basic";

import { StatisticsState, AreaItem, IspItem } from "../types";
import { ProductSelectOptions } from "@/types/common";
import { nUserModule } from "@/store/modules/nuser";
import { checkChildAccount, ctyunChildAccount, getTabConfig } from "@/config/url/statistics";
import { LoginUrl } from "@/config/url";
import { DomainActionEnum } from "../config";

@Module({ dynamic: true, store, name: "statistics" })
class Statistics extends VuexModule implements StatisticsState {
    public areaOptions: AreaItem[] = [];
    public showOverseaOfWhole = false;
    public isGlobal = true;
    public ispOptions: IspItem[] = [];
    public filteredIspOptions: IspItem[] = [];
    public allProductOptions: ProductSelectOptions[] = [];
    public isPvUvchartType = "PV";
    public dailyPeakMonthlyAverageAccount = false;
    public specialPortEnable = false;
    public childAccount = false;
    public domainCountLimit = 100;
    public speedAccount = false;
    public xosDefaultAccelerateSuffix = "";
    public zosDefaultAccelerateSuffix = "";
    public zosOriginSuffix: string[] | null = [];
    public childAccountCtiam = true; // true表示要走子账号权限   false表示不走
    public cacheTtl = 10; // 统计分析 label 过期时间，单位：秒
    public floatWinEnable = false; // 是否使用浮窗显示url
    public currentAction: DomainActionEnum = DomainActionEnum.Data; // 当前统计分析操作的action
    public targetDomainCreate = false; // 是否开启参照域名功能
    public vipBillingOpt = false; // vip计费详情操作列显示与否
    public overviewNewBilling = true; // 概览页是否使用新计费列表
    public privateBucketLimit = { ak: 40, sk: 40 }; // AK、SK长度限制
    public batchUpdate = false; // 是否开启批量修改功能
    public loadOptEnable = false; // 白屏优化开关，是否开启白屏优化后的加载流程
    public newLogDownloadEnable = false; // 新版日志下载开关
    public refreshTimedEnable = false; // 刷新定时管理开关
    public customSsl: string[] | null = []; // 自定义加密套件列表
    public gmCustomSsl: string[] | null = []; // 国密自定义加密套件列表
    public certHex = {
        // 证书加密key和iv
        key: "",
        iv: "",
    };
    public logDownloadSpan = 365; // 日志下载时间范围
    public refreshConfig = {
        urlRefreshTimeLimit: 7, // URL刷新、目录刷新、正则刷新-时间限制
        urlRefreshTimeSpanLimit: 5, // URL刷新、目录刷新、正则刷新-时间跨度限制
        urlPrefetchTimeLimit: 15, // URL预取-时间限制
        urlPrefetchTimeSpanLimit: 7, // URL预取-时间跨度限制
    };
    public ipsetWhitelist = false; // 域名配置是否可绑定ip集
    public aocdnVersionDIffLink = ""; // 安全与加速服务版本差异对比链接
    public logDownloadHelpLink = ""; // 请查看帮助文档

    get lang() {
        return nUserModule.lang;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    @Mutation
    public SET_LOAD_OPT(loadOptEnable: boolean) {
        this.loadOptEnable = loadOptEnable;
    }
    @Mutation
    public SET_CURRENT_ACTION(action: DomainActionEnum) {
        this.currentAction = action;
    }
    @Mutation
    public SET_CACHE_TTL(cacheTtl: number) {
        this.cacheTtl = cacheTtl;
    }
    @Mutation
    private SET_FLOAT_WIN_ENABLE(floatWinEnable: boolean) {
        this.floatWinEnable = floatWinEnable;
    }
    @Mutation
    private SET_AREA_LIST(list: AreaItem[] = []) {
        this.areaOptions = list;
    }

    @Mutation
    private SET_ISP_LIST(list: IspItem[] = []) {
        this.ispOptions = list;
    }

    @Mutation
    private SET_FILTER_ISP_LIST(list: IspItem[] = []) {
        this.filteredIspOptions = list;
    }

    @Mutation
    public SET_PV_UV_CHARTTYPE(chartType: string) {
        this.isPvUvchartType = chartType;
    }

    @Mutation
    private SET_SHOW_OVERSEA_OF_WHOLE(showOverseaOfWhole = false) {
        this.showOverseaOfWhole = showOverseaOfWhole;
    }

    @Mutation
    private SET_IS_GLOBAL(isGlobal = false) {
        this.isGlobal = isGlobal;
    }

    @Mutation
    private SET_ALL_PRODUCT_LIST(list: ProductSelectOptions[] = []) {
        this.allProductOptions = list;
    }
    @Mutation
    private SET_DAILY_PEAK_MONTH_AVERAGE_ACCOUNT(data: any) {
        this.dailyPeakMonthlyAverageAccount = data;
    }
    @Mutation
    private SET_XOS_DEFAULT_DOMAIN_SUFFIX(data: any) {
        this.xosDefaultAccelerateSuffix = data;
    }
    @Mutation
    private SET_ZOS_DEFAULT_DOMAIN_SUFFIX(data: any) {
        this.zosDefaultAccelerateSuffix = data;
    }
    @Mutation
    private SET_ZOS_ORIGIN_SUFFIX(data: any) {
        this.zosOriginSuffix = data;
    }
    @Mutation
    private SET_SPECIAL_PORT_ENABLE(data: any) {
        this.specialPortEnable = data;
    }
    @Mutation
    private SET_CHILD_ACCOUNT(childAccount = false) {
        this.childAccount = childAccount;
    }
    @Mutation
    private SET_DOMAIN_COUNT(limit = 100) {
        this.domainCountLimit = limit;
    }
    @Mutation
    public SET_TARGET_DOMAIN_CREATE(targetDomainCreate: boolean) {
        this.targetDomainCreate = targetDomainCreate;
    }
    @Mutation
    SET_TAB_CONFIG({ name, isTabShow }: { name: string; isTabShow: boolean }) {
        name === "speedAccount" && (this.speedAccount = isTabShow);
    }
    @Mutation
    SET_CHILD_ACCOUNT_CTIAM(val: boolean) {
        this.childAccountCtiam = val;
    }
    @Mutation
    SET_VIP_BILLING_OPT(val: boolean) {
        this.vipBillingOpt = val;
    }
    @Mutation
    SET_OVERVIEW_NEW_BILLING(val: boolean) {
        this.overviewNewBilling = val;
    }
    @Mutation
    SET_KEY_VALUE<T extends keyof Statistics>({ key, value }: { key: T; value: Statistics[T] }) {
        (this as any)[key] = value;
    }
    @Mutation
    private SET_CUSTOM_SSL(data: any) {
        this.customSsl = data;
    }
    @Mutation
    private SET_LOG_DOWNLOAD_SPAN(data: any) {
        this.logDownloadSpan = data;
    }
    @Mutation
    private SET_REFRESH_CONFIG(refreshConfig: {
        urlRefreshTimeLimit: number;
        urlRefreshTimeSpanLimit: number;
        urlPrefetchTimeLimit: number;
        urlPrefetchTimeSpanLimit: number;
    }) {
        this.refreshConfig = refreshConfig;
    }
    @Mutation
    private SET_IPSET_WHITELIST(ipsetWhitelist: boolean) {
        this.ipsetWhitelist = ipsetWhitelist;
    }

    @Action
    public async GetAreaList() {
        const {
            children = [],
            showOverseaOfWhole = false,
            isGlobal = true,
        }: { children: AreaItem[]; showOverseaOfWhole: boolean; isGlobal: boolean } = await ctFetch(
            BasicUrl.areaList
        );
        // 当包含海外数据时，对海外数据添加children，保证与其它数据格式一致
        // children.forEach((item: any) => {
        //     if (item.label === "其他") {
        //         item.children = [
        //             ...item.children.filter((item: any) => item.label !== "中国其他"),
        //             ...item.children.filter((item: any) => item.label === "中国其他"),
        //         ];
        //     }
        // });
        !isGlobal &&
            children.forEach((item: any) => {
                if (item.label === "其他") {
                    item.children = this.isCtclouds
                        ? [{ label: "其他", value: item.value }]
                        : [
                              ...item.children.filter(
                                  (childrenItem: any) => childrenItem.label !== "中国其他"
                              ),
                              ...item.children.filter(
                                  (childrenItem: any) => childrenItem.label === "中国其他"
                              ),
                          ];
                }
            });

        this.SET_SHOW_OVERSEA_OF_WHOLE(showOverseaOfWhole);
        this.SET_IS_GLOBAL(isGlobal);
        this.SET_AREA_LIST(children);
    }
    @Action
    public async GetShowOverseaOfWhole() {
        const { showOverseaOfWhole = false }: { showOverseaOfWhole: boolean } = await ctFetch(
            BasicUrl.areaList
        );
        this.SET_SHOW_OVERSEA_OF_WHOLE(showOverseaOfWhole);
    }

    @Action
    public async GetIspList() {
        const { list = [] }: { list: IspItem[] } = await ctFetch(BasicUrl.ispList);

        // 涉及【运营商】选择时，下拉选项框隐藏“多线”和“BGP”选项
        this.SET_FILTER_ISP_LIST(
            list.filter((item: IspItem) => !["007", "008"].includes(`${item.isp_code}`))
        );
        this.SET_ISP_LIST(list);
    }

    @Action
    public async nGetAllProduct() {
        const data: any = await ctFetch(BasicUrl.productList);
        !!data && this.SET_ALL_PRODUCT_LIST(JSON.parse(JSON.stringify(data.list)));
    }
    // 获取基础配置
    @Action
    public async GetBasicConfig() {
        const data: any = await ctFetch(BasicUrl.getConfig, { cache: true });
        this.SET_LOAD_OPT(data.loadOptEnable);

        this.SET_DAILY_PEAK_MONTH_AVERAGE_ACCOUNT(data.dailyPeakMonthlyAverageAccount);
        this.SET_SPECIAL_PORT_ENABLE(data.specialPortEnable);
        this.SET_XOS_DEFAULT_DOMAIN_SUFFIX(data.xosDefaultAccelerateSuffix);
        this.SET_ZOS_DEFAULT_DOMAIN_SUFFIX(data.zosDefaultAccelerateSuffix);
        // 兼容null和[]的场景
        this.SET_ZOS_ORIGIN_SUFFIX(data.zosOriginSuffix?.length ? data.zosOriginSuffix : null);
        // 是否走子账号权限
        this.SET_CHILD_ACCOUNT_CTIAM(data.childAccountCtiam);
        nUserModule.isFcdnCtyunCtclouds && this.SET_CACHE_TTL(data.cacheTtl ?? 10);
        // 是否使用浮窗显示文档
        this.SET_FLOAT_WIN_ENABLE(data.floatWinEnable ?? false);
        // 参照域名
        this.SET_TARGET_DOMAIN_CREATE(data.targetDomainCreate ?? false);
        // vip计费详情操作列显示与否
        this.SET_VIP_BILLING_OPT(nUserModule.isVip ? data.vipBillingOpt ?? false : true);
        // 概览页是否使用新计费列表
        this.SET_OVERVIEW_NEW_BILLING(data.overviewNewBilling ?? true);
        // 批量修改
        this.SET_KEY_VALUE({ key: "batchUpdate", value: data.batchUpdate ?? false });
        // 设置AK、SK长度限制
        this.SET_KEY_VALUE({
            key: "privateBucketLimit",
            value: {
                ak: data.privateBucketLimitAk ?? 40,
                sk: data.privateBucketLimitSk ?? 40,
            },
        });
        // 新版日志下载开关
        this.SET_KEY_VALUE({ key: "newLogDownloadEnable", value: data.newLogDownloadEnable ?? false });
        // 刷新定时管理开关
        this.SET_KEY_VALUE({ key: "refreshTimedEnable", value: data.refreshTimedEnable });
        // 自定义加密套件列表
        this.SET_CUSTOM_SSL(data.customSsl?.length ? data.customSsl : null);
        // 国密自定义加密套件列表
        this.SET_KEY_VALUE({
            key: "gmCustomSsl",
            value: Array.isArray(data.gmCustomSsl) ? data.gmCustomSsl : [],
        });
        // 证书加密key和iv
        this.SET_KEY_VALUE({ key: "certHex", value: { key: data.certHexKey, iv: data.certHexIv } });
        // 日志下载时间范围
        this.SET_LOG_DOWNLOAD_SPAN(data.logDownloadSpan ?? 365);
        // 刷新-时间限制/时间跨度限制
        this.SET_REFRESH_CONFIG({
            urlRefreshTimeLimit: data.refreshConfig.urlRefreshTimeLimit ?? 7,
            urlRefreshTimeSpanLimit: data.refreshConfig.urlRefreshTimeSpanLimit ?? 5,
            urlPrefetchTimeLimit: data.refreshConfig.urlPrefetchTimeLimit ?? 15,
            urlPrefetchTimeSpanLimit: data.refreshConfig.urlPrefetchTimeSpanLimit ?? 7,
        });
        this.SET_IPSET_WHITELIST(data.ipsetWhitelist ?? false);
        this.SET_KEY_VALUE({ key: "aocdnVersionDIffLink", value: data.aocdnVersionDIffLink ?? "" });
        this.SET_KEY_VALUE({ key: "logDownloadHelpLink", value: data.logDownloadHelpLink ?? "" });
    }
    // 调用 判断该账号是否为子账号 接口
    @Action
    public async GetChildAccount() {
        type childAccountType = { childAccount: boolean; limit: number };
        type ctyunChildAccountType = {
            property: { ctyunRootUserId: string; ctyunUserId: string };
            isLoggedIn: boolean;
        };

        const promises: Promise<childAccountType | ctyunChildAccountType>[] = [
            ctFetch<childAccountType>(checkChildAccount),
        ];

        const isFcdnCtyunCtclouds = nUserModule.isFcdnCtyunCtclouds;
        // ctyun体系中前端判断子账户的逻辑修改为调用新接口（/fcdn/ctyun/new/Current），根据新接口返回的字段ctyunRootUserId和ctyunUserId，这2个不一致就表明是子用户
        // 仅涉及 fcdn-ctyun(国内+国际)
        if (isFcdnCtyunCtclouds) {
            promises.push(
                ctFetch<ctyunChildAccountType>(ctyunChildAccount, { cache: true })
            );
        }

        const results = (await Promise.all(promises)) as [childAccountType, ctyunChildAccountType];
        const [{ childAccount = false, limit = 100 }, ctyunChildAccountRst] = results;

        // 兜底，如果官网登录状态过期，需要直接重定向去登录
        if (isFcdnCtyunCtclouds && ctyunChildAccountRst?.isLoggedIn === false) {
            window.location.href = LoginUrl;
        }

        let isChildAccount = isFcdnCtyunCtclouds
            ? ctyunChildAccountRst?.property?.ctyunRootUserId !== ctyunChildAccountRst?.property?.ctyunUserId
            : childAccount;

        // 如果当前用户在ctiam白名单中，则不是子账号
        if (isFcdnCtyunCtclouds && !this.childAccountCtiam) {
            isChildAccount = false;
        }

        this.SET_CHILD_ACCOUNT(isChildAccount);
        this.SET_DOMAIN_COUNT(limit);
    }
    // 根据返回来的值，来确定是否展示对应的tab
    @Action
    public async GetTabConfig() {
        const data: any = await ctFetch(getTabConfig, { cache: true });
        if (data) {
            this.SET_TAB_CONFIG(
                JSON.parse(JSON.stringify({ name: "speedAccount", isTabShow: data.speedAccount }))
            );
        }
    }
}

export const StatisticsModule = getModule(Statistics);
