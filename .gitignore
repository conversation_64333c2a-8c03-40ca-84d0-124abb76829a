.DS_Store
**/node_modules/**
**/dist
**/dist1
**/dist2
**/dist3
**/Default/**
node_modules/

# local env files
.env.local
.env.*.local

# Log files
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea
# .vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw*

# other
webpackConfig.js
deploy.config.js
yarn.lock
yarn-error.log
# dist/report.*
demo.css
debug.log
mock.*
*.pem
Default/
Local State
# package-lock.json


.count_output

.cursor/
.claude/
.env
tsconfig.tsbuildinfo
