{
  "recommendations": [
    // 代码格式化
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "editorconfig.editorconfig",

    // Vue 开发
    "octref.vetur",
    "vue.volar",

    // TypeScript 支持
    "ms-vscode.vscode-typescript-next",

    // 国际化支持
    "lokalise.i18n-ally",

    // 其他有用的插件
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "wayou.vscode-todo-highlight",
    "aaron-bond.better-comments"
  ]
}
