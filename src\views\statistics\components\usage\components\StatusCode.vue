<template>
    <div class="chart-wrap">
        <div class="title-wrap">
            <div class="operate-btn">
                <el-radio-group v-model="currentCode" size="small" @change="changeCode">
                    <el-radio-button label="">{{
                        $t("statistics.dcdn.statusCode.radioBtn")
                    }}</el-radio-button>
                    <el-radio-button label="2XX">2XX</el-radio-button>
                    <el-radio-button label="3XX">3XX</el-radio-button>
                    <el-radio-button label="4XX">4XX</el-radio-button>
                    <el-radio-button label="5XX">5XX</el-radio-button>
                </el-radio-group>
            </div>
            <div class="total">
                <div class="tip-adjust">
                    {{ $t("statistics.dcdn.statusCode.totalTip") }}
                    <span class="num">{{
                        getTotalCode
                            | convertTenThousand2Int(`${$t("statistics.dcdn.statusCode.totalTipUnit")}`)
                    }}</span>
                </div>
                <div class="tip-adjust" v-if="currentCode">
                    {{ currentCode + space + $t("statistics.dcdn.statusCode.itemTip") }}
                    <span class="num">{{
                        itemsTotal
                            | convertTenThousand2Int(`${$t("statistics.dcdn.statusCode.totalTipUnit")}`)
                    }}</span>
                </div>
            </div>
        </div>

        <v-chart
            class="chart"
            v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize
            theme="cdn"
            :options="options"
        />

        <ct-tip>
            {{ $t("statistics.dcdn.statusCode.ctTip1") }}
            <el-tooltip
                effect="dark"
                :content="$t('statistics.common.searchDownloadContent')"
                placement="right"
            >
                <i class="el-icon-download usage-download-icon" @click="downloadTable"></i>
            </el-tooltip>
        </ct-tip>

        <el-table
            :empty-text="$t('common.table.empty')"
            :data="tableData"
            stripe
            :element-loading-text="$t('statistics.common.table.loading')"
        >
            <el-table-column
                :label="$t('statistics.dcdn.statusCode.tableColumn1')"
                prop="httpCode"
            ></el-table-column>
            <el-table-column :label="$t('statistics.dcdn.statusCode.tableColumn2')" prop="httpCodeValue">
            </el-table-column>
            <el-table-column :label="$t('statistics.dcdn.statusCode.tableColumn3')">
                <template slot-scope="{ row }">
                    {{ row.percent > 0 ? row.percent.toFixed(2) : 0 }}
                </template>
            </el-table-column>
        </el-table>

        <ct-tip>{{ $t("statistics.dcdn.statusCode.ctTip2") }}</ct-tip>

        <v-chart
            class="statuscode-pie-chart"
            v-loading="loading"
            :element-loading-text="$t('statistics.common.chart.loading')"
            autoresize
            theme="cdn"
            :options="options2"
        />
    </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { mixins } from "vue-class-component";
import { StatisticsUsageUrl } from "@/config/url/statistics";
import { convertTenThousand2Int, timeFormat } from "@/filters/index";
import { SearchParams } from "@/types/statistics/usage";
import { HttpCode, HttpCodeItem, HttpCodeTotalItem } from "@/types/statistics/usage";
import { themeColorArr, THEME_AREA_STYLE } from "@/config/echart/cdn-theme";
import ChartMixin from "../chartMixin";
import { nUserModule } from "@/store/modules/nuser";

type tooltipParam = { name: string; marker: string; value: string | number };

@Component({
    name: "StatusCode",
})
export default class StatusCode extends mixins(ChartMixin) {
    currentCode = ""; // 当前选择的 code ，默认所有

    // 接口数据1
    fetchData: HttpCodeItem[] = []; // 时间戳数组
    codeTypeList: HttpCode[] = []; // code 数据中状态码数组
    downloadDataList: HttpCodeItem[] = []; // 用于下载的数据
    itemsTotal = "";

    // 包含全部状态码数据
    totalData: HttpCodeTotalItem[] = [];

    changeCode() {
        this.beforeGetData(this.searchParams);
    }
    /**
     * 再套一层函数，确保Promise.all中多个请求报错的时候能被成功提示
     */
    private async localFetchGenerator<T extends { overSize?: boolean }>(url: string, params: SearchParams) {
        const rst = await this.fetchGenerator<T>(url, params);
        if (rst.overSize) {
            this.$errorHandler({
                reason: this.$t(
                    "数据项数超过50000条，数据返回不完整，请切换到更粗的时间粒度进行查询"
                ) as string,
                code: "err",
                url: this.$route.path,
            });
        }
        return rst;
    }
    // 1、数据请求
    protected async getData(params: SearchParams) {
        let [codeData, totalData] = await Promise.all([
            this.localFetchGenerator<
                { result: HttpCodeItem[]; items: HttpCode[]; itemsTotal: any } & { overSize?: boolean }
            >(StatisticsUsageUrl.codeList, {
                ...params,
                httpCode: this.currentCode,
            }),
            this.localFetchGenerator<{ list: HttpCodeTotalItem[] } & { overSize?: boolean }>(
                StatisticsUsageUrl.codeTotal,
                params
            ),
        ]);

        if (!codeData) {
            codeData = { result: [], items: [], itemsTotal: {} };
        }
        if (!totalData) {
            totalData = { list: [] };
        }

        this.fetchData = codeData.result || [];
        this.codeTypeList = codeData.items || [];
        this.totalData = totalData.list || [];

        if (Object.keys(codeData.itemsTotal).length > 0) {
            for (const i in codeData.itemsTotal) {
                this.itemsTotal = codeData.itemsTotal[i];
            }
        } else {
            this.itemsTotal = "";
        }

        // 处理用于下载的数据
        this.downloadDataList = this.fetchData;
    }

    get space() {
        return nUserModule.lang === "en" ? " " : "";
    }

    // 根据当前选中的nxx，动态返回需要展示的表格数据
    get tableData() {
        const currentType = this.currentCode[0] || "";
        const list =
            this.totalData
                ?.filter(itm => itm.httpCodeValue)
                ?.filter(itm => {
                    return `${itm.httpCode}`.startsWith(currentType);
                }) || [];

        let totalCode = 0;
        list.forEach(item => {
            totalCode += Number(item.httpCodeValue);
        });

        return list.map(itm => {
            return {
                ...itm,
                percent: (itm.httpCodeValue / totalCode) * 100,
            };
        });
    }

    get getTotalCode() {
        return this.totalData?.reduce<number>((prev, itm) => prev + itm.httpCodeValue, 0) || 0;
    }

    // 2、数据处理
    get options() {
        const xAxisData: string[] = [];
        // 复杂类型待补充
        const seriesList: any[] = Array(this.codeTypeList.length);
        // 根据 code 类型预填充 series 配置
        this.codeTypeList.forEach((type, index) => {
            // 由于需要处理渐变色，所以不再简单的提供数据，而是生成完整配置
            seriesList[index] = {
                name: type || `${this.$t("statistics.dcdn.statusCode.vchartOptions.seriesItemName1")}`, // 待确认是否有不存在的情况，即超出 Xxx 的范围
                type: "line",
                data: [],
                areaStyle: THEME_AREA_STYLE[themeColorArr[index]],
            };
        });

        this.fetchData
            .sort((a, b) => +a.timestamp - +b.timestamp)
            .forEach(item => {
                xAxisData.push(timeFormat(+item.timestamp * 1000).replace(" ", "\n"));
                this.codeTypeList.forEach((type, index) => {
                    // 由于需要处理渐变色，所以不再简单的提供数据，而是生成完整配置
                    seriesList[index].data.push(item[type] || 0);
                });
            });

        const options = {
            ...this.comOptions,
            tooltip: {
                trigger: "axis",
                formatter: function(params: any) {
                    const summary = params.reduce((prev: number, cur: any) => {
                        return prev + cur.value;
                    }, 0);
                    let tip = "";
                    tip += params[0].name + "<br/>";
                    if (params !== null && params.length > 10) {
                        for (let i = 1; i < params.length; i++) {
                            if (i % 3 === 0) {
                                tip +=
                                    params[i].marker +
                                    params[i].seriesName +
                                    ": " +
                                    params[i].value +
                                    "&nbsp;&nbsp;&nbsp;&nbsp;" +
                                    ((params[i].value / summary) * 100).toFixed(2) +
                                    "%" +
                                    "<br/>";
                            } else {
                                tip +=
                                    "<span>" +
                                    params[i].marker +
                                    "<span>" +
                                    params[i].seriesName +
                                    ": " +
                                    "</span>" +
                                    "<span>" +
                                    params[i].value +
                                    "</span>" +
                                    "&nbsp;&nbsp;&nbsp;&nbsp;" +
                                    ((params[i].value / summary) * 100).toFixed(2) +
                                    "%" +
                                    "</span>" +
                                    "&nbsp;&nbsp;";
                            }
                        }
                        return (tip +=
                            "<span>" +
                            params[0].marker +
                            "<span >" +
                            params[0].seriesName +
                            ": " +
                            "</span>" +
                            "<span >" +
                            params[0].value +
                            "</span>" +
                            "&nbsp;&nbsp;&nbsp;&nbsp;" +
                            ((params[0].value / summary) * 100).toFixed(2) +
                            "%" +
                            "</span>" +
                            (params.length % 3 === 0 ? "<br/>" : "&nbsp;&nbsp;"));
                    } else {
                        return (
                            params[0].name +
                            "<br/>" +
                            params
                                .map((item: any) => {
                                    return (
                                        "<span>" +
                                        item.marker +
                                        "<span>" +
                                        item.seriesName +
                                        ": " +
                                        "</span>" +
                                        "<span >" +
                                        item.value +
                                        "</span>" +
                                        "&nbsp;&nbsp;&nbsp;&nbsp;" +
                                        ((item.value ? item.value / summary : 0) * 100).toFixed(2) +
                                        "%" +
                                        "</span>" +
                                        "<br/>"
                                    );
                                })
                                .join()
                                .replace(/,/g, "")
                        );
                    }
                },
            },
            legend: {
                orient: "horizontal",
                padding: [0, 150],
            },
            xAxis: {
                type: "category", // 类目轴，从 data 中获取数据
                boundaryGap: false, // 坐标轴两边留白策略
                axisLabel: {
                    formatter: this.xAxisFormatterGenerator(),
                },
                axisTick: {
                    show: false, // 为什么不显示刻度？
                },
                data: xAxisData,
            },
            yAxis: {
                type: "value", // 数值轴
                name: `${this.$t("statistics.dcdn.statusCode.vchartOptions.yAxisName")}`,
            },
            series: seriesList,
        };

        return options;
    }
    get options2() {
        const seriesData: {
            value: number;
            name: number;
        }[] = [];

        let totalCode = 0;

        this.tableData
            .sort((a, b) => b.httpCodeValue - a.httpCodeValue) // 倒叙排列
            .forEach(item => {
                totalCode += Number(item.httpCodeValue);
                seriesData.push({
                    value: item.httpCodeValue,
                    name: item.httpCode,
                });
            });

        const pieChartTitle = `${convertTenThousand2Int(totalCode)}${nUserModule.lang === "en" ? "" : "次"}`;

        const options = {
            tooltip: {
                trigger: "item",
                formatter: (params: any) =>
                    `${params.seriesName} <br/>${params.name} : ${(
                        (params.data.value / totalCode) *
                        100
                    ).toFixed(2)}%`,
            },
            legend: {
                bottom: 10,
                left: "center",
            },
            title: {
                text: pieChartTitle,
                left: "center",
                top: "45%",
                textStyle: {
                    fontFamily: "PingFangSC-Medium",
                    fontSize: pieChartTitle.length > 10 ? 18 : 24,
                    color: "#666666",
                    textAlign: "center",
                    fontWeight: 500,
                },
            },
            graphic: {
                type: "text",
                left: "center",
                top: "55%",
                style: {
                    text: this.currentCode
                        ? `${this.currentCode}${this.space}${this.$t(
                              "statistics.dcdn.statusCode.vchartOptions.itemTip"
                          )}`
                        : `${this.$t("statistics.dcdn.statusCode.vchartOptions.totalTip")}`,
                    textAlign: "center",
                    fill: "#333",
                    color: "#666666",
                    fontSize: 12,
                    fontWeight: 400,
                },
            },
            series: [
                {
                    name: `${this.$t("statistics.dcdn.statusCode.vchartOptions.seriesItemName2")}`,
                    type: "pie",
                    startAngle: 350,
                    radius: ["40%", "50%"],
                    center: ["50%", "53%"],
                    data: seriesData,
                    percentPrecision: 3,
                    itemStyle: {
                        normal: {
                            label: {
                                show: true,
                                formatter: (params: any) =>
                                    `${params.name} : ${((params.data.value / totalCode) * 100).toFixed(2)}%`,
                            },
                        },
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: "rgba(0, 0, 0, 0.5)",
                        },
                    },
                },
            ],
        };

        return options;
    }

    // 重写 excel 数据拼接方法
    protected tableToExcel() {
        let str =
            `${this.$t("statistics.dcdn.statusCode.tableToExcel.excelColumn1")}` +
            "," +
            this.codeTypeList.join(",") +
            "\n";

        // 输出格式
        str += this.downloadDataList.reduce((str, item) => {
            str += timeFormat(+item["timestamp"] * 1000) + ",";
            this.codeTypeList.forEach(type => (str += (item[type] / 1 || 0).toFixed(0) + ","));
            str += "\n";

            return str;
        }, "");

        // 增加状态码总量
        str += `${this.$t("statistics.dcdn.statusCode.tableToExcel.excelColumn2", {
            totalCode: this.getTotalCode + "\n",
        })}`;
        // 状态码量
        if (this.currentCode) {
            str +=
                this.currentCode +
                `${this.space}${this.$t("statistics.dcdn.statusCode.tableToExcel.excelColumn3", {
                    itemsTotal: this.itemsTotal,
                })}`;
        }

        this.downloadExcel({
            name: `${this.currentCode}${this.space}${this.$t(
                "statistics.dcdn.statusCode.tableToExcel.excelName"
            )}`,
            str,
        });
    }

    protected downloadTable() {
        if (this.checkListIsEmpty(this.tableData)) return;

        let str = `${this.$t("statistics.dcdn.statusCode.tableColumn1")},${this.$t(
            "statistics.dcdn.statusCode.tableColumn2"
        )},${this.$t("statistics.dcdn.statusCode.tableColumn3")}\n`;

        this.tableData.forEach(item => {
            str += item.httpCode + ",";
            str += item.httpCodeValue + ",";
            str += item.percent.toFixed(2) + ",\n";
        });

        this.downloadExcel({
            name: this.currentCode
                ? `${this.currentCode}${this.space}${this.$t(
                      "statistics.dcdn.statusCode.tableToExcel.tableName2"
                  )}`
                : `${this.$t("statistics.dcdn.statusCode.tableToExcel.tableName1")}`,
            str,
        });
    }
}
</script>

<style lang="scss" scoped>
.table-width {
    height: 280px;
}

.total {
    display: flex;
    gap: 60px;
    flex-wrap: wrap;
    row-gap: 4px;

    .tip {
        width: unset;
    }
}

.statuscode-pie-chart {
    height: 400px;
    clear: both;
    width: 100%;
}
</style>
