{"name": "aocdn", "private": true, "x-cuted": "cdn-soa-cdn-front", "brief": "a1-aocdn", "scripts": {"postinstall": "node ./scripts/postinstall", "lint": "vue-cli-service lint --fix", "format": "prettier --write \"src/**/*.{js,ts,vue,scss,css,json,md}\"", "format:check": "prettier --check \"src/**/*.{js,ts,vue,scss,css,json,md}\"", "serve": "vue-cli-service serve --open", "serve:analysis": "cross-env USE_ANALYSIS=true vue-cli-service serve --open", "serve:dev": "cross-env LOCAL_ENV=dev vue-cli-service serve --open", "serve:vip-test": "cross-env LOCAL_ENV=vip-test vue-cli-service serve --open", "serve:vip-pre": "cross-env LOCAL_ENV=vip-pre vue-cli-service serve --open", "serve:ctyun-test": "cross-env LOCAL_ENV=ctyun-test vue-cli-service serve --open", "serve:ctyun-pre": "cross-env LOCAL_ENV=ctyun-pre vue-cli-service serve --open", "serve:intl-test": "cross-env LOCAL_ENV=intl-test vue-cli-service serve --open", "serve:intl-pre": "cross-env LOCAL_ENV=intl-pre vue-cli-service serve --open", "serve:bs": "cross-env PLATFORM=bs vue-cli-service serve --open", "build": "vue-cli-service build", "build:analysis": "cross-env USE_ANALYSIS=true vue-cli-service build", "build:bs": "cross-env PLATFORM=bs vue-cli-service build", "build:aocdn": "cross-env PLATFORM=aocdn vue-cli-service build", "build:fcdn": "cross-env PLATFORM=fcdn vue-cli-service build", "test:unit": "jest"}, "dependencies": {"@cdnplus/autoform": "^0.1.2", "@cdnplus/common": "^0.1.5", "@cutedesign/ct-monaco": "^1.1.6", "@cutedesign/ui": "2.0.6", "alogic-base-web": "2.0.8", "core-js": "^3.6.5", "crypto-js": "4.2.0", "dayjs": "^1.10.7", "echarts": "^5.0.2", "element-ui": "^2.15.8", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "moment": "^2.29.1", "qiankun": "2.10.16", "resize-detector": "^0.2.2", "statistic-loader": "1.0.4", "svg-sprite-loader": "^6.0.11", "vue": "^2.6.14", "vue-class-component": "^7.2.3", "vue-i18n": "8.28.2", "vue-property-decorator": "^8.4.2", "vue-router": "=3.0.7", "vuex": "^3.4.0", "vuex-router-sync": "^5.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/plugin-transform-object-set-prototype-of-to-assign": "^7.12.13", "@babel/preset-env": "^7.26.0", "@types/jest": "^27.5.2", "@types/lodash-es": "^4.17.6", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "^4.5.0", "@vue/cli-plugin-eslint": "^4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "@vue/vue2-jest": "^27.0.0", "babel-jest": "^29.7.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-require-context": "^0.1.1", "chalk": "^4.1.0", "cross-env": "^7.0.3", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "jest": "^27.5.1", "lint-staged": "^9.5.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "patch-package": "^8.0.0", "postcss-plugin-namespace": "0.0.3", "prettier": "^1.19.1", "pretty-quick": "^3.1.0", "regenerator-runtime": "^0.13.7", "sass": "=1.32.12", "sass-loader": "^8.0.2", "shelljs": "^0.8.4", "speed-measure-webpack-plugin": "^1.5.0", "ts-jest": "^27.1.5", "tslib": "^2.1.0", "typescript": "~3.9.3", "vue-template-compiler": "^2.6.14", "vuex-module-decorators": "^1.0.1", "webpack-bundle-analyzer": "^4.10.1", "xss": "^1.0.13", "yorkie": "^2.0.0"}, "engines": {"node": ">=10", "pnpm": ">=5"}, "postcss": {"plugins": {"autoprefixer": {}}}, "gitHooks": {"commit-msg": "node ./scripts/verifyCommit.js"}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": ["prettier --write", "tsc -p ./tsconfig.json --noEmit && echo", "vue-cli-service lint ./src --fix && echo"], "*.{css,scss,less,json,md}": ["prettier --write"]}, "volta": {"node": "14.21.3"}}