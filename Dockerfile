# 使用 FROM 指定基础镜像，后面操作都是基于它进行定制
FROM harbor.ctyuncdn.cn/cdngslbconf/nginx1.12.5-x86-arm:1.1.0

MAINTAINER anylogic

# 设定环境变量
ENV NGINX_STATUS=http://localhost/status/format/json

# 创建必要目录（合并RUN指令减少层数）
RUN mkdir -p /www/h5/aocdn && \
    mkdir -p /www/h5/aocdnbs && \
    mkdir -p /usr/local/nginx/conf

# 复制nginx配置
COPY config/nginx.conf /usr/local/nginx/conf/

# 复制构建产物
COPY dist1 /www/h5/aocdn/

# 设置工作目录
WORKDIR /www/h5/aocdn

