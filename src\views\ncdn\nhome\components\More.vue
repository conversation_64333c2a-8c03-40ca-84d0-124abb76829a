<template>
    <!-- 信息中心 -->
    <ct-box :tags="$t('home.info.title')" :class="{ 'box-pad': type === '1' || type === '2' }">
        <template #tags-slot>
            <el-radio-group v-model="type" size="mini" class="time-radio" @change="onTypeChange">
                <el-radio-button class="time-radio-button" label="1">{{
                    $t("home.info.tab[0]")
                }}</el-radio-button>
                <el-radio-button class="time-radio-button" label="2">{{
                    $t("home.info.tab[1]")
                }}</el-radio-button>
                <el-radio-button class="time-radio-button" label="3">{{
                    $t("home.info.tab[2]")
                }}</el-radio-button>
                <el-radio-button class="time-radio-button" label="4">{{
                    $t("home.info.tab[3]")
                }}</el-radio-button>
            </el-radio-group>

            <el-link
                v-show="type === '3' && moreQuestionUrl"
                :underline="false"
                class="go-url more-btn"
                @click="go(moreQuestionUrl)"
            >
                {{ $t("home.more") }}
            </el-link>
            <el-link
                v-show="type === '4' && moreNewsUrl"
                :underline="false"
                class="go-url more-btn"
                @click="go(moreNewsUrl)"
            >
                {{ $t("home.more") }}
            </el-link>
        </template>
        <el-tabs v-model="type" type="card" class="tab-wrapper">
            <div class="el-tab-pane" :label="$t('home.info.tab[0]')" v-if="type === '1'">
                <el-scrollbar
                    v-loading="loading"
                    style="height: calc(100% - 44px); overflow-y: auto; padding-right: 12px;"
                >
                    <div class="no-msg" v-if="messageList.list.length === 0">
                        {{ $t("home.info.noData") }}
                    </div>
                    <div v-else class="msg" v-for="item in messageList.list" :key="item.msgId">
                        <el-button
                            type="text"
                            class="msg-title text-ellipsis"
                            :title="item.msgTitle || messageTypeMap[item.type]"
                            @click="showAnnouncement(item.msgId)"
                        >
                            <i class="icon-a-linklianjie"></i>
                            {{ item.msgTitle || messageTypeMap[item.type] }}
                            <span class="msg-top" v-if="item.displayOrder === '0'">{{
                                $t("home.info.top")
                            }}</span>
                        </el-button>
                        <div class="msg-date">{{ +item.doneDate | timeFormat }}</div>
                    </div>
                </el-scrollbar>
                <el-pagination
                    small
                    layout="prev, pager, next, total"
                    :total="+messageList.total"
                    :page-size="10"
                    @current-change="messagePageChange"
                    hide-on-single-page
                />
            </div>

            <div class="el-tab-pane" :label="$t('home.info.tab[1]')" v-if="type === '2'">
                <el-scrollbar
                    v-loading="loading"
                    native
                    style="height: calc(100% - 44px); overflow-y: auto; padding-right: 12px;"
                >
                    <div class="no-msg" v-if="domainMsgList.list.length === 0">
                        {{ $t("home.info.noData") }}
                    </div>
                    <div v-else>
                        <div class="msg" v-for="item in domainMsgList.list" :key="item.id">
                            <el-button
                                type="text"
                                class="msg-title text-ellipsis"
                                :title="item.content"
                                @click="showMsg(item.content)"
                            >
                                <i class="icon-a-linklianjie"></i>
                                {{ item.content }}
                            </el-button>
                            <div class="msg-date">{{ item.createTime }}</div>
                        </div>
                    </div>
                </el-scrollbar>
                <el-pagination
                    class="pager"
                    small
                    layout="prev, pager, next, total"
                    :total="+domainMsgList.total"
                    :page-size="10"
                    @current-change="domainPageChange"
                    hide-on-single-page
                />
            </div>
            <div class="el-tab-pane" :label="$t('home.info.tab[2]')" v-if="type === '3'">
                <el-scrollbar
                    v-loading="loading"
                    style="height: calc(100% - 44px); overflow-y: auto; padding-right: 12px;"
                >
                    <div class="no-msg" v-if="commonQuestion.length === 0">{{ $t("home.info.noData") }}</div>
                    <div>
                        <div class="msg" v-for="item in commonQuestion" :key="item.id">
                            <el-button
                                type="text"
                                class="msg-title text-ellipsis"
                                :title="item.title"
                                @click="go(item.href)"
                            >
                                <i class="icon-a-linklianjie"></i>
                                {{ item.title }}
                            </el-button>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
            <div class="el-tab-pane" :label="$t('home.info.tab[3]')" v-if="type === '4'">
                <el-scrollbar
                    v-loading="loading"
                    style="height: calc(100% - 44px); overflow-y: auto; padding-right: 12px;"
                >
                    <div class="no-msg" v-if="guideNews.length === 0">{{ $t("home.info.noData") }}</div>
                    <div>
                        <div class="msg" v-for="item in guideNews" :key="item.id">
                            <el-button
                                type="text"
                                class="msg-title text-ellipsis"
                                :title="item.title"
                                @click="go(item.href)"
                            >
                                <i class="icon-a-linklianjie"></i>
                                {{ item.title }}
                            </el-button>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </el-tabs>
        <el-dialog
            :visible.sync="dialogVisible"
            append-to-body
            :show-close="false"
            modal-append-to-body
            :close-on-click-modal="false"
            class="dialog-more"
        >
            <div class="content-wrapper">
                <h3 class="title">
                    {{ msgData.title }}
                </h3>
                <div class="subhead">
                    <span>{{ msgData.createTime }}</span>
                    <span>
                        <span style="margin-right: 20px">
                            {{ $t("home.info.msgType") }}：{{ messageTypeMap[msgData.type] }}
                        </span>
                        <span>{{ $t("home.info.from") }}</span>
                    </span>
                </div>
                <div class="content" v-html="msgData.content" />
                <div style="text-align: center">
                    <el-button type="primary" @click="dialogVisible = false">
                        {{ $t("common.dialog.close") }}
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </ct-box>
</template>

<script lang="ts">
import { nBannerList, nGetBBS, nListBBS, nMsgList } from "@/config/url";
import { Component, Vue, Watch } from "vue-property-decorator";
import { messageFetchData, bbsFetchData, bbsContent } from "@/types/home";
import { timeFormat } from "@/filters";
import { bannerData } from "@/types/common";
import { nUserModule } from "@/store/modules/nuser";
import { getAnnouncementDomain } from "@/utils/logic/url";

@Component({
    filters: {
        timeFormat(data: string): string {
            return timeFormat(data) || data;
        },
    },
})
export default class More extends Vue {
    private type = "1";
    private currentMsgPageNum = 1;
    private currentDomainPageNum = 1;
    private loading = false;
    private dialogVisible = false;
    private messageList: bbsFetchData = { list: [], total: "0" };
    private domainMsgList: messageFetchData = { list: [], total: "0" };
    private commonQuestion: bannerData[] = [];
    private guideNews: bannerData[] = [];
    private moreQuestionUrl = "";
    private moreNewsUrl = "";
    private msgData = {};
    private messageTypeMap = Object.freeze({
        1: this.$t("home.info.msgTypes[0]"),
        2: this.$t("home.info.msgTypes[1]"),
        3: this.$t("home.info.msgTypes[2]"),
        4: this.$t("home.info.msgTypes[3]"),
    });
    $docHelp: any;

    get lang() {
        return nUserModule.lang;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }

    @Watch("type")
    onTypeChange(val: string) {
        if (val === "1") this.getAnnounce(this.currentMsgPageNum);
        if (val === "2") this.getData(4, this.currentDomainPageNum);
        if (val === "3") this.getTextList(val, this.getFAQCode());
        if (val === "4") this.getTextList(val, this.getManualCode());
        if (val === "3" || val === "4") {
            this.getTextList("5", this.getDomain("cdn.more.btn"));
        }
    }

    protected getFAQCode() {
        const code = "cdn.common.question";
        if (this.isCtclouds) {
            return this.getDomain(code);
        }
        if (nUserModule.isCtyun && this.lang === "en") {
            return `${code}.ct.en`;
        }
        return code;
    }
    protected getManualCode() {
        const code = "cdn.guide.news";
        if (this.isCtclouds) {
            return this.getDomain(code);
        }
        if (nUserModule.isCtyun && this.lang === "en") {
            return `${code}.ct.en`;
        }
        return code;
    }
    created() {
        this.getAnnounce();
    }

    protected go(url: string) {
        this.$docHelp(url);
    }
    private getDomain(domain: string) {
        if (this.isCtclouds) {
            return `${domain}.${this.lang}`;
        } else return domain;
    }

    private async getTextList(type: string, domain: string) {
        this.loading = true;
        const rst = await this.$ctFetch<{ list?: bannerData[] }>(nBannerList, {
            cache: true,
            data: {
                domain: domain,
            },
        });
        if (type === "3") this.commonQuestion = rst.list || [];
        if (type === "4") this.guideNews = rst.list || [];
        if (type === "5") {
            this.moreQuestionUrl = rst.list?.[0].href || "";
            this.moreNewsUrl = rst.list?.[1].href || "";
        }
    }
    protected async getData(type: number, pageIndex = 1, pageSize = 10) {
        this.loading = true;

        const rst = await this.$ctFetch<any>(nMsgList, {
            data: {
                type, // 1：系统公告消息 2：活动消息 3：通知消息 4： 域名消息
                pageIndex,
                pageSize,
                language: this.lang,
            },
        });
        this["domainMsgList"].list = rst.results || [];
        this["domainMsgList"].total = rst.total;
    }
    private async getAnnounce(pageNum = 1, pageSize = 10) {
        this.loading = true;
        const rst = await this.$ctFetch<any>(nListBBS, {
            data: {
                msgClassId: getAnnouncementDomain(),
                limit: pageSize,
                offset: pageSize * (pageNum - 1),
            },
        });
        this.messageList.list =
            rst.list.sort((a: bbsContent, b: bbsContent) => {
                return (
                    (b.displayOrder === "0" ? 10000000000000 : 0) +
                    +b.doneDate -
                    (a.displayOrder === "0" ? 10000000000000 : 0) -
                    +a.doneDate
                );
            }) || [];
        this.messageList.total = rst.total;
    }

    protected showMsg(msg: string) {
        this.$alert(msg, this.$t("home.info.domain") as string, {
            confirmButtonText: this.$t("common.dialog.submit") as string,
        });
    }
    protected async showAnnouncement(msgId: string) {
        const rst = await this.$ctFetch<any>(nGetBBS, { data: { msgId } });
        this.dialogVisible = true;
        this.msgData = {
            title: rst.msgTitle,
            type: "1",
            content: rst.htmlContent,
            createTime: timeFormat(+rst.doneDate),
            id: "RANDOM_STRING",
            isTop: rst.displayOrder === "0",
        };
    }
    protected messagePageChange(pageNum: number) {
        this.currentMsgPageNum = pageNum;
        this.getAnnounce(pageNum);
    }
    protected domainPageChange(pageNum: number) {
        this.currentDomainPageNum = pageNum;
        this.getData(4, pageNum);
    }
}
</script>

<style lang="scss" scoped>
.tab-wrapper {
    clear: both;
    .el-tabs--card > .el-tabs__header {
        display: none;
    }
    .el-scrollbar {
        margin: 0;
    }
}

.ct-section-wrap .tab-wrapper .el-scrollbar {
    margin: 0;
    height: 100%;
}

// 公告信息
.no-msg {
    text-align: center;
    line-height: 180px;
    font-size: 14px;
}

.msg {
    display: flex;
    line-height: 36px;
    height: 36px;
    border-bottom: 1px solid $border-color;

    .msg-title {
        flex: 1;
        text-align: left;
        .icon-a-linklianjie {
            display: inline-block;
            background-image: url(~@/assets/images/link.svg);
            background-size: 100% 100%;
            width: 12px;
            height: 12px;
            vertical-align: middle;
            margin-top: -4px;
        }
        &.el-button--text {
            color: $text-color;
            &:hover {
                color: $color-master-hover;
            }
        }
    }
    .msg-top {
        background-image: url(~@/assets/images/pullTopBackground.png);
        background-size: 100% 100%;
        display: inline-block;
        width: 37px;
        height: 16px;
        font-size: 12px;
        text-indent: 8px;
        vertical-align: middle;
        line-height: 16px;
        margin-left: 4px;
        color: #fff;
    }

    .msg-date {
        flex: 135px 0; // ff 下渲染的内容尺寸大于 chrome
        text-align: right;
        margin-right: 20px;
    }
}
.pager {
    text-align: right;
    margin-top: 8px;
}
.go-url {
    margin-left: 0;
    width: 100%;
    text-align: left;
    color: $color-neutral-10;
    border-bottom: 1px solid $border-color;
    padding-bottom: 8px;
    &:hover {
        color: $color-master-active;
    }

    i {
        float: right;
    }
}
.more-btn {
    color: $color-master;
    float: right;
    width: auto;
    border: none;
    padding: 0;
    line-height: 28px;
}

.tab-wrapper {
    height: 100%;
    ::v-deep {
        .el-dialog__body {
            padding: 0;
            height: 100%;
        }
        .el-dialog__header {
            padding: 0;
            border-bottom: 0;
        }
        .el-message-box {
            width: 700px;
        }
        .el-tabs__content {
            height: 100%;
        }
        .el-tabs__header {
            margin: 0;
        }
        .el-tab-pane {
            height: calc(100% - 44px);
        }
        .el-scrollbar {
            height: 100%;
            .el-scrollbar__wrap {
                margin-bottom: 0px !important;
                margin-right: 0px !important;
                overflow: auto !important;
            }
        }
        .el-button + .el-button {
            margin-left: 0;
        }
    }
}

::v-deep {
    .el-radio-group .el-radio-button--mini .el-radio-button__inner {
        display: inline-block;
        width: auto;
    }
}
.dialog-more ::v-deep {
    .el-dialog__header {
        display: none;
    }
    .content-wrapper {
        padding: 0;

        .title {
            font-size: 16px;
            margin-bottom: 8px;
            color: $text-color;
            font-weight: bold;
            span {
                vertical-align: middle;
            }
            .top {
                display: inline-block;
                padding: 0 8px;
                font-size: 14px;
                line-height: 24px;
                margin-right: 4px;
                color: $color-danger-hover;
                border: 1px solid $color-danger-hover;
                border-radius: 3px;
            }
        }

        .subhead {
            line-height: 1.5;
            font-size: 12px;
            margin-bottom: 20px;
            color: $color-info;
            display: flex;
            justify-content: space-between;
            span {
                display: inline-block;
            }
        }
        .content {
            padding: 0 20px;
            margin-bottom: 30px;
            ol,
            ul,
            li {
                list-style: decimal;
            }
        }
    }
}
</style>
