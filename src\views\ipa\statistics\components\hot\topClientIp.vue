<template>
    <el-card>
        <el-table
            max-height="425px"
            :data="fetchData"
            v-loading="loading"
            :element-loading-text="$t('statistics.common.table.loading')"
        >
            <el-table-column :label="$t('statistics.rank.common.tableColumn1')" prop="rank" align="center" />
            <el-table-column label="IP" prop="ip" align="center" />
            <el-table-column :label="$t('statistics.IP归属')" prop="ip_ownership" min-width="100" />
            <el-table-column
                :label="$t('statistics.rank.domainRank.tableColumn3')"
                prop="flow"
                align="center"
            />
            <el-table-column :label="$t('statistics.eas.tab[2]')" prop="connections" align="center" />
        </el-table>
    </el-card>
</template>

<script>
import { statistics as StatisticsUrl } from "@/config/url/ipa/statistics";
import { convertFlowB2P } from "@/utils";
import rankMixin from "../rankMixin";
import chartShim from "../chartShim";
import { cloneDeep } from "lodash-es";

export default {
    name: "topClientIp",
    mixins: [rankMixin, chartShim],
    props: {
        domainOptions: {
            type: Array,
            default: () => [],
        },
        areaOptions: {
            type: Array,
            default: () => [],
        },
        instNameOptions: {
            type: Array,
            default: () => [],
        },
        hasInstName: {
            type: Boolean,
            default: false,
        },
    },
    mounted() {
        this.$ctBus.$on("ipa:download:hot:topClientIp", this.downloadTable);
    },
    beforeDestroy() {
        this.$ctBus.$off("ipa:download:hot:topClientIp");
    },
    data() {
        return {
            form: {
                domain: [],
                province: [],
                type: 1,
            },
            fetchData: [],
            loading: false,
            downloadDataList: [],
            totalRequest: "",
            totalFlow: "",
        };
    },
    computed: {
        domainPlaceholder() {
            let message = `${this.$t("statistics.common.domainSelectOption")}`;
            if (this.form.type === 2) {
                message = `${this.$t("statistics.eas.tip6")}`;
            }
            return message;
        },
        domainInstNameOptions() {
            let data = cloneDeep(this.domainOptions);
            if (this.form.type === 2) {
                data = cloneDeep(this.instNameOptions);
            }
            return data;
        },
        domainType() {
            let data = "domain";
            if (this.form.type === 2) {
                data = "inst_name";
            }
            return data;
        },
    },
    methods: {
        customRenderWrapper() {
            this.updateAgency(null, "topClientIp");
        },
        async initData(reqParam) {
            const params = {
                ...reqParam,
                top_n: 100,
                province_code: reqParam.province,
            };
            delete params.province;

            this.getData(params);
        },
        // 接口获取数据
        async getData(params) {
            this.loading = true;
            const loadingIndex = this.loadingIndex++;

            const rst = await this.$ctFetch(StatisticsUrl.ipRanking, {
                method: "POST",
                transferType: "json",
                body: { data: params },
            });
            if (loadingIndex !== this.loadingIndex - 1) return console.warn("find different loading index");
            this.loading = false;
            // this.downloadDataList = this.fetchData = rst.result;
            this.downloadDataList = rst.result
                .map(item => ({
                    ...item,
                }))
                .sort((a, b) => +a.rank - +b.rank);

            this.fetchData = rst.result
                .map(item => ({
                    ...item,
                    flow: convertFlowB2P(item.flow, this.scale).result,
                }))
                .sort((a, b) => +a.rank - +b.rank);
            // ***获取总连接数和总流量
            // this.totalRequest = "1";
            // this.totalFlow = "2";
            this.totalRequest = 0;
            this.totalFlow = 0.0;
            rst.result.map(item => {
                this.totalRequest += item.connections;
                this.totalFlow += item.flow;
            });
        },
        downloadTable() {
            if (!Array.isArray(this.fetchData) || !this.fetchData.length) {
                this.$message(`${this.$t("statistics.common.chart.errMsg[2]")}`);
                return true;
            }

            this.download();
        },
        // 表格下载
        download() {
            this.tableToExcel();
        },
        // 重写 excel 数据拼接方法
        tableToExcel() {
            let str = "";

            str = `${this.$t("statistics.rank.domainRank.tableColumn1")},IP,${this.$t(
                "statistics.rank.domainRank.tableColumn3"
            )}(${this.MB}),${this.$t("statistics.eas.tab[2]")}\n`;

            this.downloadDataList.forEach(item => {
                str += `${item.rank},${item.ip},${(item.flow / Math.pow(this.scale, 2)).toFixed(2)},${
                    item.connections
                }\n`;
            });

            //增加峰值带宽和95峰值带宽
            str += `\n${this.$t("statistics.rank.common.tableToExcel.excelColumn2")}\n${this.$t(
                "statistics.eas.excel.tip1"
            )}${this.totalRequest} \n${this.$t("statistics.eas.excel.tip2")}${(
                this.totalFlow / Math.pow(this.scale, 2)
            ).toFixed(2)} ${this.MB}\n`;

            //表格CSV格式和内容
            this.downloadExcel({
                name: `${this.$t("statistics.eas.tip12")}`,
                str,
            });
        },
        resetQuery() {
            // 由于子组件每个都会导入chartShim, 所以都是独立维护一份queryForm数据，所以这里直接清空就可以了
            this.form = {
                province: [],
                domain: [],
            };
        },
        hot_type_change() {
            this.form.domain = [];
            this.updateAgency(null, "topClientIp");
        },
    },
};
</script>
<style lang="scss" scoped>
.search-bar {
    display: flex;
    gap: 12px;
}
</style>
