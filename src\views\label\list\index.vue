<template>
    <ct-section-wrap :headerText="$t('label.title')">
        <template #header>
            <ct-section-header :title="$t('label.title')" :tip="$t('label.slogan')"></ct-section-header>
        </template>
        <ct-box class="table-scroll-wrap">
            <div class="search-bar-wrapper">
                <div>
                    <el-button type="primary" class="el-icon-plus" @click="openAddDialog">{{
                        $t("label.addTag")
                    }}</el-button>
                    <el-button type="primary" @click="batchDelete" :disabled="!batchSelectedTags.length">{{
                        $t("ipSet.批量删除")
                    }}</el-button>
                </div>
                <div class="search-bar">
                    <label class="search-label">{{ $t("label.keyword") }}</label>
                    <el-input size="medium" maxlength="255" v-model="keyword">
                        <el-select
                            size="medium"
                            slot="prepend"
                            v-model="keywordType"
                            :placeholder="$t('label.select')"
                            :class="[isEn && 'increse-width']"
                        >
                            <el-option
                                v-for="item in searchOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-input>
                    <div class="search-btns">
                        <el-button size="medium" @click="reset">{{ $t("label.reset") }}</el-button>
                    </div>
                </div>
            </div>

            <el-table
                :data="showList"
                :tree-props="treeProps"
                row-key="labelId"
                class="lv2bg"
                v-loading="loading"
                :empty-text="$t('common.table.empty')"
            >
                <!-- 将name根据层级分列展示 -->
                <el-table-column :label="$t('label.tagGroup')" min-width="100">
                    <template #default="{ row }">
                        <span v-if="row.children && row.children.length" class="label-group-name">{{
                            row.name
                        }}</span>
                        <el-checkbox
                            v-else-if="row.children && !row.children.length"
                            class="label-group-name"
                            @change="val => handleBatchTagSelect(val, row)"
                            >{{ row.name }}</el-checkbox
                        >
                        <el-checkbox
                            v-else
                            :disabled="row.state !== '0' || (row.children && row.children.length > 0)"
                            @change="val => handleBatchTagSelect(val, row)"
                        ></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column
                    :label="$t('label.tagName')"
                    min-width="100"
                    :formatter="row => (row.children ? '' : row.name)"
                />
                <el-table-column prop="note" :label="$t('label.tagDescription')" min-width="200" />
                <el-table-column prop="create_date" :label="$t('label.createdAt')" width="200" />
                <el-table-column :width="isEn ? '150' : '100'">
                    <template #header>
                        {{ $t("label.status") }}
                        <el-tooltip effect="dark" :content="$t('label.associated')" placement="top">
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <template #default="{ row }">
                        <!-- 只有标签名行才展示关联状态 -->
                        <!-- <span :class="{ circle: true, active: row.state !== '0' }" v-if="!row.children">{{
                            row.state !== "0" ? "有关联" : "无关联"
                        }}</span> -->
                        <div v-if="!row.children">
                            <cute-state v-if="row.state !== '0'" type="master">{{
                                $t("label.yes")
                            }}</cute-state>
                            <cute-state v-else type="info">{{ $t("label.no") }}</cute-state>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('common.table.operation')" min-width="150">
                    <template #default="{ row }">
                        <!-- 只有标签名行才展示操作按钮 -->
                        <el-button v-if="!row.children" type="text" @click.stop="openDetailDialog(row)">
                            {{ $t("label.details") }}
                        </el-button>
                        <el-button
                            :disabled="row.state !== '0' || (row.children && row.children.length > 0)"
                            type="text"
                            @click.stop="deleteTag(row)"
                        >
                            {{ $t("label.delete") }}
                        </el-button>
                        <el-button v-if="!row.children" type="text" @click.stop="openEditDialog(row)">
                            {{ $t("label.edit") }}
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </ct-box>
        <el-dialog
            :title="$t('label.addTag')"
            :visible.sync="addDialogVisible"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            width="600px"
            custom-class="add-dialog-wrapper"
        >
            <el-form
                :model="addForm"
                :rules="addRules"
                ref="addForm"
                v-loading="loading"
                :label-width="isEn ? '120px' : '100px'"
            >
                <el-form-item :label="$t('label.tagGroup')" prop="groupName">
                    <el-select
                        filterable
                        allow-create
                        :placeholder="$t('label.selectGroupName')"
                        v-model="addForm.groupName"
                        class="label-group w-full"
                    >
                        <el-option
                            v-for="key in dataList"
                            :key="key.labelId"
                            :label="key.name"
                            :value="key.name"
                        />
                    </el-select>
                </el-form-item>

                <el-form-item :label="$t('label.tagName')" prop="name">
                    <el-input
                        type="textarea"
                        :rows="3"
                        v-model="addForm.name"
                        :placeholder="$t('label.enterTagNames')"
                    />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="addDialogVisible = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="submitAddForm">{{ $t("common.dialog.submit") }}</el-button>
            </template>
        </el-dialog>
        <el-dialog
            :title="$t('label.tagDetails')"
            :visible.sync="detailDialogVisible"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
        >
            <el-form :label-width="isEn ? '120px' : '100px'">
                <el-form-item :label="$t('label.tagGroup')">
                    <span>{{ detailTag.groupName }}</span>
                </el-form-item>

                <el-form-item :label="$t('label.tagName')">
                    <span>{{ detailTag.name }}</span>
                </el-form-item>

                <el-form-item :label="$t('label.tagDescription')">
                    <span>{{ detailTag.note }}</span>
                </el-form-item>

                <el-form-item :label="$t('label.createdAt')">
                    <span>{{ detailTag.create_date }}</span>
                </el-form-item>

                <el-form-item :label="$t('label.bindDomain')">
                    <p v-for="(domain, index) in detailTag.domainList" :key="index">{{ domain }}</p>
                </el-form-item>
            </el-form>
        </el-dialog>
        <el-dialog
            :title="$t('label.editTag')"
            :visible.sync="editDialogVisible"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
        >
            <el-form
                :model="editForm"
                :rules="editRules"
                ref="editForm"
                v-loading="loading"
                :label-width="isEn ? '120px' : '100px'"
            >
                <el-form-item :label="$t('label.tagGroup')">
                    <span>{{ editForm.groupName }}</span>
                </el-form-item>

                <el-form-item :label="$t('label.tagName')">
                    <span>{{ editForm.name }}</span>
                </el-form-item>
                <el-form-item :label="$t('label.tagDescription')" prop="note">
                    <el-input
                        type="textarea"
                        :rows="3"
                        v-model="editForm.note"
                        :placeholder="$t('label.enterLabelDesc')"
                    />
                </el-form-item>
                <el-form-item :label="$t('label.createdAt')">
                    <span>{{ editForm.create_date }}</span>
                </el-form-item>
                <el-form-item :label="$t('label.bindDomain')" prop="domainList">
                    <domain-select
                        class="domain-select-wrapper"
                        v-model="editForm.domainList"
                        :multiple="true"
                        :domainOptions="domainOptions"
                    />
                </el-form-item>
            </el-form>
            <div slot="footer" v-loading="dialogLoading">
                <el-button @click="editDialogVisible = false">{{ $t("common.dialog.cancel") }}</el-button>
                <el-button type="primary" @click="submitEditForm">{{ $t("common.dialog.submit") }}</el-button>
            </div>
        </el-dialog>
    </ct-section-wrap>
</template>

<script lang="ts">
import { Component, Watch, Vue, Ref } from "vue-property-decorator";
import { ElForm } from "element-ui/types/form";
import { DomainModule } from "@/store/modules/domain";
import { LabelUrl } from "@/config/url/label";
import { DomainActionEnum, getCtiamAction, GetCtiamButtonAction, getDomainAction } from "@/config/map";
import { LabelModule } from "@/store/modules/label";
import { InterfaceLabel, InterfaceLabelItem } from "@/store/types";
import DomainSelect from "@/components/domainsSelect/index.vue";
import { throttleFn } from "@/utils/logic/index";
import { nUserModule } from "@/store/modules/nuser";
import { checkCtiamButtonAuth } from "@/store/modules/menu";

type LabelValueItem = InterfaceLabel & {
    state: string; // 绑定的域名个数，其实用不到
    domains: string[]; // 绑定的域名列表，用于生成映射关系
    domainList?: string[];
};
@Component({ components: { DomainSelect } })
export default class LabelList extends Vue {
    protected searchUrl = LabelUrl.tree;
    private keyword = "";
    private keywordType = "key";
    private addDialogVisible = false;
    private dialogLoading = false;
    private detailDialogVisible = false;
    private editDialogVisible = false;
    private addForm = {
        groupName: "",
        name: "",
    };
    private detailTag = {
        groupName: "",
        name: "",
        note: "",
        // eslint-disable-next-line @typescript-eslint/camelcase
        create_date: "",
        domainList: [],
    };
    private editForm = {
        groupName: "",
        name: "",
        // eslint-disable-next-line @typescript-eslint/camelcase
        create_date: "",
        labelId: "",
        note: "",
        domainList: [],
        parentId: "",
    };
    private batchSelectedTags: {
        tagId: string;
        isTagGroup: boolean;
    }[] = [];

    @Ref("addForm") readonly addFormRef!: ElForm;
    @Ref("editForm") readonly editFormRef!: ElForm;

    get isFcdnCtyunCtclouds() {
        return nUserModule.isFcdnCtyunCtclouds;
    }
    private get dataList() {
        return LabelModule.nativeList;
    }
    private get isEn() {
        return nUserModule.lang === "en";
    }
    private get showList() {
        // 根据三种搜索词类型过滤展示列表
        if (this.keywordType === "key") return this.dataList.filter(item => item.name.includes(this.keyword));
        else if (this.keywordType === "value") {
            const dataList: InterfaceLabelItem[] = JSON.parse(JSON.stringify(this.dataList));
            for (let i = 0; i < dataList.length; ) {
                dataList[i].children = dataList[i].children?.filter(value =>
                    value.name.includes(this.keyword)
                );
                if (dataList[i].children?.length === 0) dataList.splice(i, 1);
                else i++;
            }
            return dataList;
        } else {
            const dataList: InterfaceLabelItem[] = JSON.parse(JSON.stringify(this.dataList));
            for (let i = 0; i < dataList.length; ) {
                dataList[i].children = dataList[i].children?.filter(value =>
                    value.note?.includes(this.keyword)
                );
                if (dataList[i].children?.length === 0) dataList.splice(i, 1);
                else i++;
            }
            return dataList;
        }
    }
    private get loading() {
        return LabelModule.loading || DomainModule[this.domainAction].loading;
    }
    private get treeProps() {
        return {
            children: "children",
        };
    }

    private get searchOptions() {
        return [
            {
                value: "key",
                label: this.$t("label.tagGroup"),
            },
            {
                value: "value",
                label: this.$t("label.tagName"),
            },
            {
                value: "note",
                label: this.$t("label.tagDescription"),
            },
        ];
    }
    // 全部的域名列表
    get domainList() {
        return DomainModule[DomainActionEnum.Domain].list;
    }
    get domainAction() {
        // fcdn-ctyun 体系（国内+国际）需要取ctiam权限管控下的域名数据
        return this.isFcdnCtyunCtclouds ? getDomainAction("Label") : DomainActionEnum.Data;
    }
    get domainOptions() {
        return DomainModule[this.domainAction].options;
    }
    get addRules() {
        return {
            groupName: [
                { required: true, message: this.$t("label.enterTagGroupName"), trigger: "blur" },
                {
                    pattern: "^[\\w\\u4e00-\\u9fa5-]+$",
                    message: this.$t("label.validCharacters"),
                    trigger: "blur",
                },
                { max: 32, message: this.$t("label.lengthLimit"), trigger: "blur" },
            ],
            name: [
                { required: true, message: this.$t("label.enterTagNamePrompt"), trigger: "blur" },
                {
                    pattern: "^[\\w\\u4e00-\\u9fa5-]+(;[\\w\\u4e00-\\u9fa5-]+)*;?$",
                    message: this.$t("label.enterTagNames"),
                    trigger: "blur",
                },
                {
                    validator: (_rule: any, value: string, callback: Function) => {
                        const values = value.split(";");

                        // 检查当前输入框的标签名有无重复
                        const count: { [key: string]: boolean } = {};
                        if (
                            values.some(value => {
                                if (value.length > 64) {
                                    callback(this.$t("label.tagNameLengthLimit", { value }) as string);
                                    return true;
                                }
                                if (count[value]) {
                                    callback(this.$t("label.tagGroupTagDuplicate", { value }) as string);
                                    return true;
                                }
                                count[value] = true;
                                return false;
                            })
                        )
                            return callback();

                        // 检查当前标签组名下的标签名有无重复
                        const parent = this.dataList.find(item => item.name === this.addForm.groupName);
                        if (
                            !parent ||
                            !values.every(value => parent.children?.some(item => item.name === value))
                        )
                            callback();
                        else callback(this.$t("label.tagGroupTagDuplicate", { value }) as string);
                    },
                },
            ],
        };
    }

    get editRules() {
        return {
            note: [{ max: 10000, message: this.$t("label.maxLengthLimit"), trigger: "blur" }],
            domainList: [],
        };
    }

    @Watch("keywordType")
    onKeywordTypeChange() {
        this.keyword = ""; // 切换搜索词类型时清空输入框
    }

    private created() {
        this.getLabelTree();
    }
    private async openAddDialog() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("labelListAdd"));

        Object.assign(this.addForm, { groupName: "", name: "" });
        this.addDialogVisible = true;
    }
    private async handleBatchTagSelect(val: boolean, row: LabelValueItem) {
        if (val) {
            this.batchSelectedTags.push({
                isTagGroup: Object.keys(row).includes("children"),
                tagId: row.labelId,
            });
        } else {
            this.batchSelectedTags = this.batchSelectedTags.filter(itm => itm.tagId !== row.labelId);
        }
    }
    private async batchDelete() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("labelListBatchDelete"));
        const hasTag = this.batchSelectedTags.some(itm => !itm.isTagGroup);
        const hasTagGroup = this.batchSelectedTags.some(itm => itm.isTagGroup);
        const tip =
            hasTag && hasTagGroup
                ? this.$t("label.确认删除选中的标签及标签组？")
                : hasTag
                ? this.$t("label.确认删除选中的标签？")
                : this.$t("label.确认删除选中的标签组？");

        await this.$confirm(tip as string, this.$t("ipSet.批量删除") as string, {
            confirmButtonText: this.$t("common.dialog.submit") as string,
            cancelButtonText: this.$t("common.dialog.cancel") as string,
            type: "warning",
            closeOnPressEscape: false,
        });

        await this.$ctFetch(LabelUrl.batchDelete, {
            method: "POST",
            data: {
                labelIds: this.batchSelectedTags.map(itm => itm.tagId),
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.batchSelectedTags = [];
        this.getLabelTree(true);
        this.$message.success(this.$t("label.deleteSuccess") as string);
    }
    @throttleFn(5)
    private async submitAddForm() {
        await this.$ctUtil.formValidate2Promise(this.addFormRef);
        await this.addFormRef.validate();
        await this.$ctFetch(LabelUrl.batchCreate, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: { data: this.addForm },
        });
        this.getLabelTree();
        this.$message.success(this.$t("label.createTagSuccess") as string);
        this.addDialogVisible = false;
    }
    /**
     * 获取标签
     * @param preserveSearchState 获取前是否需要保留之前的标签数据
     */
    private async getLabelTree(preserveSearchState = false) {
        await LabelModule.GetLabelList({
            cache: false,
            action: getCtiamAction("Label"),
            preserveData: preserveSearchState, // 保持数据不被清空
        });
    }

    private getParent(row: LabelValueItem) {
        return this.dataList.find(item => item.children?.some(value => value.labelId === row.labelId));
    }

    private async openDetailDialog(row: LabelValueItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("labelView"));
        Object.assign(
            this.detailTag,
            {
                groupName: this.getParent(row)!.name,
                name: "",
                note: "",
                // eslint-disable-next-line @typescript-eslint/camelcase
                create_date: "",
                domainList: [],
            },
            row
        );
        this.detailDialogVisible = true;
    }

    private async deleteTag(row: InterfaceLabelItem | LabelValueItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("labelListDelete"));

        const isTag = !Object.keys(row).includes("children");
        const tip = isTag ? this.$t("label.确认删除选中的标签？") : this.$t("label.确认删除选中的标签组？");

        await this.$confirm(tip as string, this.$t("label.delete") as string, {
            confirmButtonText: this.$t("common.dialog.submit") as string,
            cancelButtonText: this.$t("common.dialog.cancel") as string,
            type: "warning",
            closeOnPressEscape: false,
        });
        await this.$ctFetch(LabelUrl.delete, {
            method: "POST",
            data: {
                labelId: row.labelId,
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.getLabelTree(true); // 保持搜索状态
        this.$message.success(this.$t("label.deleteSuccess") as string);
    }

    private async openEditDialog(row: LabelValueItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("labelListEdit"));

        this.isFcdnCtyunCtclouds &&
            (await DomainModule.GetDomainList({ action: getDomainAction("Label"), shouldCheckCache: true }));
        const parent = this.getParent(row);
        Object.assign(
            this.editForm,
            {
                groupName: parent!.name,
                parentId: parent?.labelId,
                name: "",
                // eslint-disable-next-line @typescript-eslint/camelcase
                create_date: "",
                labelId: "",
                note: "",
                domainList: [],
            },
            row
        );
        this.editDialogVisible = true;
    }
    private async submitEditForm() {
        this.dialogLoading = true;
        try {
            const { note, name, labelId, domainList, parentId } = this.editForm;
            const data = { note, name, labelId, domainList, parentId };
            await this.$ctUtil.formValidate2Promise(this.editFormRef);

            await this.$ctFetch(LabelUrl.update, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: { data },
            });
            this.getLabelTree();
            this.$message.success(this.$t("label.editTagSuccess") as string);
            this.editDialogVisible = false;
        } catch (error) {
            this.$errorHandler(error);
        } finally {
            this.dialogLoading = false;
        }
    }
    private reset() {
        this.keyword = "";
        this.getLabelTree();
    }
}
</script>

<style lang="scss" scoped>
// 选择资源时，调整二级树持续保持背景色，用于提示用户
::v-deep .el-tree.lv2bg > .el-tree-node > .el-tree-node__children > .el-tree-node > .el-tree-node__content {
    background: #f7f8fa;
}
.search-bar-wrapper {
    display: flex;
    justify-content: space-between;
}
.search-bar .search-btns {
    display: inline;
}
.label-group {
    ::v-deep .el-input {
        width: 220px;
    }
}
.search-label {
    margin-right: 16px;
}

.w-full {
    width: 100%;
    ::v-deep .el-input {
        width: 100% !important;
    }
}
.increse-width {
    min-width: 140px !important;
}
::v-deep {
    .add-dialog-wrapper {
        height: 350px !important;
    }
}

.domain-select-wrapper {
    ::v-deep {
        .domain-select__item {
            line-height: 1.5 !important;
        }
        .el-scrollbar {
            margin: 16px 0 !important;
        }
    }
}

.label-group-name {
    word-break: inherit;
}
</style>
