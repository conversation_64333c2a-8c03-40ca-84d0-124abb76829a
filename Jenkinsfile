@Library('cdn-devops') _

def RELEASE_BUILD
String BUILD_RESULT = ""

pipeline {
    agent {
        label 'jnlp-slave'
    }
  options {
    buildDiscarder(logRotator(numToKeepStr: '10'))
    disableConcurrentBuilds()
    skipDefaultCheckout()
    timeout(time: 30, unit: 'MINUTES')
    gitLabConnection('gitlab')
  }
    environment {
        IMAGE_CREDENTIALS = "credential-harbor"
        BUILD_CONTEXT = "target"

        // 镜像仓库地址
        IMAGE_REPOSITORY = "harbor.ctyuncdn.cn/test/cdn-front"
        IMAGE_REPOSITORY_FCDN = "harbor.ctyuncdn.cn/test/fcdn-front"
        IMAGE_REPOSITORY_FCDN_BS = "harbor.ctyuncdn.cn/test/fcdn-front-bs"

        // 配置文件的路径
        DEPLOY_BASE_DIR = "deploy/base"
        DEPLOY_VIP_DIR = "deploy/vip"
        DEPLOY_CTYUN_DIR = "deploy/ctyun"


        // {{POD_PRE}}-web-{{POD_POST}} 定义名字
        POD_PRE_VIP = "cdn"
        POD_POST = "front"

        POD_PRE_VIP_BS = "cdn"
        POD_POST_BS = "front-bs"
        // 执行条件（通用）
        DEV_BRANCH = "develop" // 后面根据需要调整开发环境分支名的限制
        QA_TAG = "(test)?v\\d+\\.\\d+.\\d+(.\\d+)?(-rc\\d+)?" // v1.0.0-rc1
    }

    stages {
        stage('Checkout') {
            steps {
                script {
                    container('tools') {
                        // checkout code
                        retry(2) { scmVars = checkout scm }
                        RELEASE_BUILD = scmVars.GIT_COMMIT
                        BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"Checkout OK...  √")
                        echo 'begin checkout...'
                        echo sh(returnStdout: true, script: "env")
                    }
                }
            }
        }

        stage('npm install') {
            when {
                expression { BRANCH_NAME ==~ env.QA_TAG || BRANCH_NAME ==~ env.QA_BRANCH }
            }
            steps {
                script {
                    container('tools') {
                                                sh """
                        # 清理旧的构建产物
                        rm -rf ./dist ./dist1 ./dist2 ./dist3

                        # 安装依赖
                        npm install
                        """
                    }
                }
            }
        }

        stage('npm build') {
            when {
                expression { BRANCH_NAME ==~ env.QA_TAG || BRANCH_NAME ==~ env.QA_BRANCH }
            }
            parallel {
                stage('build aocdn') {
                    steps {
                        script {
                            container('tools') {
                                retry(2) {
                                    sh """
                                    npm run build:aocdn
                                    """
                                }
                                BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"aocdn-build OK...√")
                            }
                        }
                    }
                }
                stage('build fcdn') {
                    steps {
                        script {
                            container('tools') {
                                retry(2) {
                                    sh """
                                    npm run build:fcdn
                                    """
                                }
                                BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"fcdn-build OK...√")
                            }
                        }
                    }
                }
                stage('build fcdn bs') {
                    steps {
                        script {
                            container('tools') {
                                retry(2) {
                                    sh """
                                    npm run build:bs
                                    """
                                }
                                BUILD_RESULT = devops.updateBuildTasks(BUILD_RESULT,"bs-build OK...√")
                            }
                        }
                    }
                }
            }
        }

        stage('build images parallel') {
            when {
                expression { BRANCH_NAME ==~ env.QA_TAG || BRANCH_NAME ==~ env.QA_BRANCH }
            }
            parallel {
                stage('build image aocdn') {
                    steps {
                        script {
                            container('ecx-docker-with-buildx') {
                                devops.dockerBuild(
                                    "Dockerfile", //Dockerfile
                                    ".", // build context
                                    IMAGE_REPOSITORY, // repo address
                                    RELEASE_BUILD, // tag
                                    IMAGE_CREDENTIALS, // credentials for pushing
                                ).buildxAndPush("linux/arm64,linux/amd64")
                            }
                        }
                    }
                }
                stage('build image fcdn') {
                    steps {
                        script {
                            container('ecx-docker-with-buildx') {
                                devops.dockerBuild(
                                    "DockerfileFcdn", //Dockerfile
                                    ".", // build context
                                    IMAGE_REPOSITORY_FCDN, // repo address
                                    RELEASE_BUILD, // tag
                                    IMAGE_CREDENTIALS, // credentials for pushing
                                ).buildxAndPush("linux/arm64,linux/amd64") // 只构建amd64以节省时间
                            }
                        }
                    }
                }
                stage('build image fcdn bs') {
                    steps {
                        script {
                            container('ecx-docker-with-buildx') {
                                devops.dockerBuild(
                                    "DockerfileBS", //Dockerfile
                                    ".", // build context
                                    IMAGE_REPOSITORY_FCDN_BS, // repo address
                                    RELEASE_BUILD, // tag
                                    IMAGE_CREDENTIALS, // credentials for pushing
                                ).buildxAndPush("linux/arm64,linux/amd64") // 只构建amd64以节省时间
                            }
                        }
                    }
                }
            }
        }
        // stage('deploy') {
        //     when {
        //         expression { BRANCH_NAME ==~ env.QA_TAG || BRANCH_NAME ==~ env.QA_BRANCH }
        //     }

        //     stages {
        //         stage("deploy-vip") {
        //             steps {
        //                 // 替换占位，不能直接用环境变量获取
        //                 sh """
        //                     cp -f ${DEPLOY_BASE_DIR}/deploy.yaml ${DEPLOY_VIP_DIR}/deploy.yaml
        //                     cp -f ${DEPLOY_BASE_DIR}/service.yaml ${DEPLOY_VIP_DIR}/service.yaml
        //                     sed -i 's#{{POD_PRE}}#'$POD_PRE_VIP'#g' ${DEPLOY_VIP_DIR}/deploy.yaml ${DEPLOY_VIP_DIR}/service.yaml
        //                     sed -i 's#{{POD_POST}}#'$POD_POST'#g' ${DEPLOY_VIP_DIR}/deploy.yaml ${DEPLOY_VIP_DIR}/service.yaml
        //                 """

        //                 script {
        //                     container('tools') {
        //                         // create configmap and ingress
        //                         // devops.deploy("", "${DEPLOY_VIP_DIR}/ingress.yaml","",false).start()
        //                         dep = devops.deploy(
        //                             "${DEPLOY_VIP_DIR}", //k8s files dir
        //                             "${DEPLOY_VIP_DIR}/deploy.yaml",
        //                             RELEASE_BUILD,
        //                             false
        //                         )
        //                         dep.start()
        //                     }
        //                 }
        //             }
        //         }
        //     }
        // }
    }


    post {
        success {
            script {
                container('tools') {
                    devops.notificationSuccess("cdnplus-web", "流水线完成了", RELEASE_BUILD, "dingTalk")
                }
            }
        }
        failure {
            script {
                container('tools') {
                    devops.notificationFailed("cdnplus-web", "流水线失败了", RELEASE_BUILD, "dingTalk")
                }
            }
        }
    }

}
