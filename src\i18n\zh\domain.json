{"title": "域名管理", "comfirm": "确认", "comfirm2": "删除确认", "comfirm3": "确定启用", "domainList": "域名列表", "domainEdit": "域名编辑", "domainCreate": "新增域名", "domainBatch": "批量修改", "domainView": "查看域名", "update": "更新", "add": "新增", "add2": "添加", "add3": "新增", "disable": "停用", "enable": "启用", "or": " 或 ", "operate": "操作", "view": "查看", "edit": "编辑", "more": "更多", "delete": "删除", "modify": "修改", "enter": "请输入", "cancel": "取消", "submit": "确 定", "submit1": "确定提交", "type": "类型", "back": "返回", "submit2": "提交保存", "close": "关闭", "unknown": "未知", "areaScope": ["中国内地", "全球(不含中国内地)", "全球"], "content": "内容", "list": {"note": "提示", "opennote": "开启提示", "remainsopen": "仍然开启", "title": "域名列表", "titleTip": "展示已启用和已停用的域名。新增域名、启用域名、停用域名需要配置。", "createBtn": "添加域名", "batchCreateBtn": "批量添加", "batchEditBtn": "批量修改", "allTypes": "全部类型", "placeholder1": "请输入域名关键字", "status": ["所有状态", "已启用", "已停止", "配置中"], "actions": ["停用", "启用", "删除", "新增", "更新"], "refresh": "刷新", "reset": "重置", "export": "导出", "tableTip1": "此域名是加速域名 CNAME 到 CDN 节点上的地址，查看", "tableTip2": "配置指引", "tableLabel1": "编号", "tableLabel2": "域名", "tableLabel3": "加速类型", "tableLabel4": "加速区域", "tableLabel5": "状态", "tableLabel6": "创建时间", "tableLabel7": "标签", "addLabel": "新增", "tip1": "您提交的内容审核服务变更工单正在进行中。", "tip2": "您提交的{action}工单正在进行中，", "tip3": "请输入原因，最多128字", "tip4": "请选择批量修改的域名，批量修改限制每次最多{domainCountLimit}个域名", "tip5": "很抱歉，当前批量修改仅适用于加速类型为静态加速和视频点播加速", "tip6": "很抱歉，您选择了多个加速类型的域名，当前批量修改仅支持每次修改同一个加速类型。", "tip7": "很抱歉，请选择当前操作状态可编辑的域名", "tip8": "很抱歉，每次最多修改{domainCountLimit}个域名。", "tip9": "该域名叠加了多个产品，即配置信息被多个产品共用，此次修改将同步修改共用产品的配置信息，点击确认继续", "tip10": "您有工单正在进行中。暂时不能执行{title}操作。", "tip11": "危险操作，请输入需要删除的域名：{domain}", "tip12": "域名输入错误", "tip13": "当前域名已被停止加速，不允许启用。", "tip14": "当前域名无法启用，您可以购买对应流量包或开通按量计费产品后再重新启用域名。", "tip15": "该产品{status}，请恢复产品再启用该域名配置！", "tip16": "暂未检测到备案信息，将在24小时内二次审核，未备案域名会被封禁，为避免影响您的CDN服务，请及时备案。", "tip16-1": "备案校验", "tip17": "您的域名未备案，无法使用CDN服务，如有疑问请转", "tip17-1": "工单咨询。", "tip18": "请再次确认是否需要{title}该域名？该操作请慎重。", "tip19": "您{title}域名的请求已发出", "tip20": "您尚未开通CDN产品或流量包用尽，请完成订购后尝试新增域名。", "label1": "停用域名", "label2": "停用确认", "label3": "停用原因", "downloadStr": "序号,域名,cname,加速类型,状态,创建时间,源站地址,源站角色,源站权重,回源HOST,http回源端口,https回源端口,回源host,是否跟随请求端口回源", "downloadName": "域名列表", "tip21": "域名点击停用后，天翼云接入层CNAME将立刻解析至不可访问地址！若您操作的停用域名仍解析至天翼云CNAME，请您及时将域名解析至正常可服务的地址，或重新接入天翼云CDN服务，避免业务受本次操作影响。", "tableLabelipv6": "IPv6解析", "tableLabelipv6Tooltip": "开启后支持IPv6解析，提供IPv6双栈节点服务", "tip22": "CDN加速进制或系数与原有产品存在不一致，请确认要将加速类型变更为CDN加速？变更加速类型可能会影响计费，请谨慎操作。", "tableLabelPremiumNetwork": "高性能网络", "tableLabelPremiumNetworkScene": "高性能网络场景", "tableLabelPremiumNetworkTip1": "场景一：适用于域名加速区域为全球（不含中国内地），且用户在中国大陆", "tableLabelPremiumNetworkTip2": "场景二：适用于域名加速区域为全球，用户在中国大陆，源站在海外", "tableLabelPremiumNetworkTip3": "场景三：适用于域名加速区域为全球或全球（不含中国内地），用户在海外，源站在中国大陆", "tableLabelPremiumNetworkTip4": "请确认是否关闭高性能网络增值服务?", "tableLabelPremiumNetworkTip5": "开启高性能网络，请选择适用场景：", "tableLabelPremiumNetworkTip6": "高性能网络开关下发成功，等待一段时间之后生效", "tableLabelPremiumNetworkTip7": "当前已开启高性能网络配置，需关闭后才能变更加速区域。", "scene": ["场景一", "场景二", "场景三"], "domainCountLimitTip": {"tip1": "CDN加速域名个数配额不足，详见：", "tip2": "使用限制。"}}, "create": {"title": "添加域名", "title2": "批量添加域名", "titleTip": "您创建的域名将会以工单形式提交。", "reTitle": "重新创建域名", "reTip": "重新创建一个域名", "resubmit": "重新发起", "basicInfo": "基本信息", "domainName": "加速域名", "tip1": "加速域名为需要使用加速服务的域名。支持泛域名，如*.ctyun.cn。", "btn1": "新增域名", "acceType": "加速类型", "tip2": "加速类型一经确认，无法自助修改，请根据您的业务类型谨慎选择。", "domainType": "域名类型", "acceRegion": "加速区域", "serviceArea": "服务区域", "IPv6": "ipv6开关", "originSetting": "源站设置", "tip3-1": "提交工单", "tips3": "如果客户端有Range请求，CDN默认无法缓存对应文件，回源流量将远大于预期，造成额外的回源流量费用。建议您通过<a target='_blank' href={orderLink} class='link'>提交工单</a>，开启  <a target='_blank' href={documentOriginLink}  class='link'>完整文件回源</a> 或 <a target='_blank' href={rangeOriginLink} class='link'>Range回源</a> 功能。", "tip3-6": "如果客户端有Range请求，CDN默认无法缓存对应文件，回源流量将远大于预期，造成额外的回源流量费用。建议您通过<a target='_blank' href={orderLink} class='link'>提交工单</a>，开启完整文件回源或Range回源功能。", "originServer": "源站", "originServer-1": "新增源站", "originServer-2": "修改源站", "originEncodeSelf": "回源加密算法", "originEncodeSelfPlaceholder": "未配置默认国际加密回源", "proxyGmsslModeList": ["国密加密回源", "国际加密回源", "跟随客户端加密算法回源"], "number": "序号", "originType": "源站类型", "level": "层级", "weight": "权重", "originHost": "指定源站回源HOST", "addOrigin": "+新增源站", "tip4": "支持IP或域名，最多可添加60个。", "tip5": "支持IP或域名、媒体存储源站，最多可添加60个。", "tip5-1": "支持IP或域名、媒体存储、对象存储源站，最多可添加60个。", "tip5-2": "支持IP或域名、对象存储源站，最多可添加60个。", "originPolicy": "回源协议", "tip6": "HTTP协议回源默认使用80端口，HTTPS协议回源默认使用443端口，跟随请求协议回源将根据请求指定的协议回源到您源站的80或443端口，自定义回源端口范围为1-65535。", "dynamicPolicy": "动态回源策略", "originPort": "回源端口", "originHost2": "回源HOST", "tip7": "选择“择优回源”时，优先回最快的源站，忽略权重；选择“按权重回源”时，按照配置的权重回源，选择“保持登录”时基于客户端IP哈希回源。", "tip8": "默认回源host决定了回源请求访问到源站的哪个站点，默认值为加速域名。自定义配置时，请确保您的源站有配置相应的HOST。", "https": "Https配置", "httpsSwitch": "Https开关", "httpsUpload": "Https证书上传", "certAlias": "证书备注名", "placeholder1": "请选择证书", "tip9": "可更换证书（请留意证书有效期），若未找到目标证书，", "tip9-1": "点击上传", "https2Switch": "HTTP2.0", "tls": "TLS版本", "placeholder2": "请选择TLS版本，不配置默认全选", "cache": "缓存设置", "tip10": "支持自定义生效顺序，优先级数字大则优先生效。", "accessControl": "访问控制", "ip": "IP黑白名单", "tip11": "通过黑/白名单来对访问者身份进行识别和过滤，支持IPV6地址填写。", "referer": "Referer防盗链", "referer2": "是否允许空referer访问", "referer3": "是否允许空协议", "referer4": "匹配所有端口", "referer5": "忽略大小写", "referer6": "是否追加", "tip12": "通过黑/白名单来对访问者身份进行识别和过滤。", "ua": "UA黑白名单", "matchMethod": {"label": "匹配方式", "option1": "正则", "option2": "通配符"}, "uri": "URL黑白名单", "tip13": "源站地址不能和加速域名相同", "tip14": "不能存在相同的源站地址", "tip15": "输入的IP或源地址格式错误，请检查", "tip16": "输入的IP是保留地址，请检查", "tip17": "输入的权重必须为1-100间的整数", "tip18": "必须存在一个主源", "tip19": "输入的回源HOST格式不正确", "tip20": "指定源站回源HOST和默认回源HOST不能同时配置，指定源站回源HOST优先级高于默认回源HOST，不能同时配置", "tip21": "请输入证书备注名", "tip21-1": "请输入证书", "tip22": "请选择加速类型", "tip23": "请选择加速区域", "tip24": "请输入域名类型", "tip25": "请先实名认证，并重新登录后再试", "tip26": "部分域名新增失败", "tip27": "操作成功", "tip28": "校验结果如下", "tip29": "成功", "tip30": "失败，失败原因检查如下", "tip31": "请输入域名或等待域名校验完成后再操作", "tip32": "成功提交证书", "tip33": "工单正在创建中，请稍后查询", "tip34": "确认删除所选择的源站", "tip35": "吗？", "title3": "添加自有证书", "title4": "更新自有证书", "tip36": "请上传证书（请留意证书有效期）。", "certName": "证书备注名", "certs": "证书公钥", "placeholder3": "请输入证书公钥", "key": "证书私钥", "placeholder4": "请输入证书私钥", "placeholder5": "请选择或输入媒体存储源站", "xos0": "IP或域名", "xos1": "媒体存储源站", "tip38": "输入的证书备注名不正确,证书备注名仅支持中英文字符、数字、_、-、*、.、()组合", "tip39": "长度不超过255个字符", "tip40": "证书备注名已存在", "tip41": "长度不超过65535个字符", "tip42": "当前媒体存储源站仅支持国内区域，使用媒体存储作为源站可为您节省更多CDN回源流量费用。", "tip43": "什么是媒体存储源站?", "tip44-1": "请注意：", "tip44": "1.媒体存储源的域名必须以{endPointStr}结尾，如果手动输入必须是已开通的Bucket域名。", "tip45": "2.回媒体存储源需要同时将回源host修改成Bucket域名，请通过“指定源站回源host”功能进行配置。", "primary": "主", "secondary": "备", "tip46": "跳转域名列表中，请稍候~", "tip47": "全站加速不支持媒体存储源站配置，请将源站配置中，媒体存储源站相关配置删除", "tip47-1": "全站加速不支持对象存储源站配置，请将源站配置中，对象存储源站相关配置删除", "tip48": "请先选择加速类型", "tip49": "配置开启后将产生额外费用。未购买静态HTTPS请求包之前，均默认按静态HTTPS请求数的{0}计费。购买静态HTTPS请求包之后，将优先用请求包抵扣产生的静态HTTPS请求数，超出后则按量付费。", "tip49-1": "按量标准资费", "tip50": "3.媒体存储CDN回源流量支持按需+资源包计费。如您开通的媒体存储为包周期（用量封顶模式），则回源流量会作为公网流出流量计费，请确认您的计费方式。", "tip51": "具体可参考：", "tip52": "CDN回源流量计费说明。", "tip53": "开关打开后，输入框内容自动清空，请填写需要追加的内容，本次提交为增量下发。", "routeSelectionMethod": "选路方式", "tip54": "全站加速、全站加速-上传加速、全站加速-websocket加速，三者收费不同，详见：", "tip55": "全站加速计费", "tip56": "上传加速计费", "tip57": "websocket加速计费", "tip58": "全站加速、全站加速-上传加速、全站加速-websocket加速，三者收费不同。", "tip59": "、", "tip60": "。", "zos": {"tip1": "对象存储源站", "tip2": "请输入对象存储源站", "tip3": "对象存储源站必须以{endPointStr}结尾", "tip4": "对象存储源站和媒体存储源站不能同时配置", "tip5": "只能配置一个对象存储 或 媒体存储源站", "tip6": "当前对象存储源站仅支持国内区域，使用对象存储作为源站可为您节省更多CDN回源流量费用。", "tip7": " 什么是对象存储源站?", "tip8": "1.对象存储源的域名必须以{endPointStr}结尾。", "tip9": "2.回对象存储源需要同时将回源host修改成Bucket域名，请通过“指定源站回源host”功能进行配置。"}, "copy": {"tip1": "参照域名", "tip2": "请先选择参照域名", "tip3": "除以下回源配置、Https配置和UDFScript外，其他加速配置根据参照域名进行设置；全站加速参照域名仅支持选择同加速类型&同域名类型。选中参考域名后，可查看参照域名配置详情，此处仅展示通用配置，若该域名存在特殊配置（例如QUIC功能），请联系客服人员查看。", "tip4": "参照已有域名配置", "tip5": "加速配置", "tip6": "自定义配置", "tip7": "查看参照域名配置详情", "tip8": "域名生效配置请以实际测试为准，如配置不符合预期，请联系客服人员进行配置调整。"}, "encryptionSuite": {"label1": "加密套件", "label2": "自定义加密套件", "option1": "全部加密套件", "option2": "强加密套件", "option3": "自定义加密套件", "placeholder1": "默认全部加密套件", "tip1": "请选择自定义加密套件"}}, "detail": {"title": "查看域名", "btn": "点击查看", "tip1": "您提交的{action}工单正在进行中，", "tip2": "变更加速区域时不支持变更域名配置，建议先完成域名配置变更，再变更加速区域。", "tip3": "该域名叠加了多个产品，即配置信息被多个产品共用，此次修改将同步修改共用产品的配置信息，点击确认继续", "tip4": "是否确定修改该域名？", "basicInfo": "基本信息", "label1": "域名名称", "label2": "域名状态", "label3": "加速类型", "label5": "域名类型", "label6": "加速区域", "label7": "创建时间", "label8": "ipv6开关", "label9": "缓存设置", "label10": "状态码设置", "label11": "Http响应头", "label12": "缓存参数", "label13": "缓存URI改写", "label14": "Referer防盗链", "label15": "IP黑/白名单", "label16": "UA黑/白名单", "label17": "URL鉴权", "label18": "压缩设置", "label19": "Https配置", "label20": "Https证书上传", "label21": "OCSP Stapling", "label22": "强制跳转", "label23": "源站配置", "label24": "回源协议", "label25": "回源跟随", "label26": "回源HTTP请求头", "label27": "回源URI改写", "label28": "私有Bucket回源", "label29": "忽略回源参数", "label30": "回源参数改写", "label31": "脚本名称", "label32": "缓存规则", "label33": "过期时间", "label34": "去问号缓存", "label35": "custom定制缓存", "label36": "不缓存", "label37": "优先遵循源站", "label38": "强制缓存", "label39": "缓存 URL", "label40": "其他文件", "label41": "状态码", "label42": "状态码缓存", "label43": "参数", "label44": "取值", "label45": "Http响应头", "label46": "忽略参数", "label47": "指定参数", "label48": "优先级", "label49": "待改写PATH", "label50": "目标PATH", "label51": "不忽略", "label52": "全部忽略", "label53": "保留指定参数", "label54": "忽略指定参数", "label55": "鉴权类型", "label56": "加密key", "label57": "加密元素分隔符", "label58": "时间戳加密参数", "label59": "加密参数", "label60": "MD5加密参数", "label61": "鉴权URL有效时长", "label62": "文件压缩", "label63": "压缩类型", "label64": "最小压缩文件大小", "label65": "压缩文件方式", "label66": "压缩文件类型", "label67": "最小压缩文件", "label68": "压缩方式", "label69": "OCSP Stapling开关", "label70": "跳转类型", "label71": "跳转方式", "label72": "回源302/301跟随", "label73": "回源host配置是否有效", "label74": "跟随次数上限", "label75": "忽略所有回源参数", "label76": "改写参数模式", "label77": "参数名:参数值", "label78": "参数名", "label79": "参数值", "label80": "追加", "label81": "覆盖", "label82": "回源参数改写", "label83": "未绑定业务脚本", "label84": "外链改造层数", "label85": "改造生效范围", "label86": "IPv6请求", "label87": "IPv4&IPv6请求", "label88": "外链改造黑名单", "label89": "点击类外链改造", "label90": "缓存时间规则", "label91": "flv拖拉", "label92": "拖拉模式", "label93": "起始参数", "label94": "结尾参数", "label95": "mp4拖拉", "label96": "开启", "label97": "关闭", "label98": "静态加速", "label99": "下载加速", "label100": "视频点播加速", "label101": "视频直播", "label102": "全站加速", "label103": "安全加速", "label104": "CDN加速", "label105": "应用加速", "label106": "Web应用防火墙(边缘云版)", "label107": "DDoS高防(边缘云版)", "label108": "smart dns", "label109": "网站安全监测", "label110": "下载加速(闲时)", "label111": "极速直播", "label112": "分布式安全云平台", "label113": "容器安全平台", "label114": "爬虫管理", "label115": "GTM", "label116": "边缘安全与加速", "label117": "零信任服务", "label118": "重保产品", "label119": "AccessOne 边缘接入服务", "label120": "上传加速", "label121": "保持参数顺序", "label122": "是否对参数编码", "label123": "优先遵循源站不缓存头", "label124": "IP黑白名单集合", "label125": "IP集名称", "tip5": "未更新", "tip6": "确认要将加速类型变更为CDN加速？变更加速类型可能会影响计费，请谨慎操作。", "tip7": "提交变更加速类型工单成功！", "tip8": "您当前未开通CDN加速产品，请先前往<a target='_blank' style='color: #0004ed;' href='{link}'>CDN加速</a>开通。", "tip9": "支持配置自定义资源的缓存过期时间规则，支持指定路径或者文件名后缀方式。", "tip10": "全局默认优先遵循源站缓存。", "tip11": "默认开启去问号参数缓存，若需要带问号后参数缓存，请选择关闭该功能。", "tip12": "权重支持自定义生效顺序，优先级数字大则优先生效。", "tip13": "custom定制缓存暂不支持自助修改", "tip14": "设置异常状态码过期时间，支持范围 ", "tip15": "管理回源HTTP响应头，可支持新增、修改、删除，返回特定的HTTP Header参数信息给客户端。", "tip16": "参数仅支持大小写字母、数字、下划线、中划线。", "tip17": "不支持中文及字符。", "tip18": "若不填则为删除对应响应头。", "tip19": "保留指定参数示例：a=$arg_a&b=$arg_b，其中$arg_a代表问号后参数a的值。", "tip20": "忽略指定参数示例：'a,b'，多个参数以英文逗号分隔。", "tip21": "存在特殊配置，暂不支持自助修改。若需要调整请提交<a target='_blank' href='{orderLink}'>客服工单</a>或者联系客服人员处理。", "tip22": "支持对缓存URI进行改写，可配置多条改写规则，支持正则表达式，从上到下按顺序依次执行可以匹配的所有规则。", "tip23": "了解更多", "tip24": "以/开头的URI，不含http://头及域名、?及参数。支持正则表达式，如^/test$。", "tip25": "以/开头的URI，不含http://头及域名、?及参数。支持正则表达式，常用$1、$2来捕获待改写PATH中圆括号内的字符串。", "tip26": "请避免出现重复状态码，请检查", "tip27": "请按照格式输入符合的3xx或4xx或5xx状态码", "tip28": "参数已存在", "tip29": "取值不支持中文文字和字符", "tip30": "文件类型输入格式有误，请检查后再确定！", "tip31": "请避免出现重复", "tip31-1": "，请检查", "tip32": "确定删除所选择的缓存配置吗", "tip33": "确认删除所选择的状态码设置？", "tip34": "确认删除该配置项？", "tip35": "开启URL鉴权功能CDN会对加密串及时间戳进行校验，有效保护站点资源。", "tip36": "存在特殊配置，不能修改，若需要调整请联系客服处理。", "tip37": "最多{maxNum}个，使用换行符分隔，支持通配，支持 IP ，支持端口，如：\n*.test.com\ntest.com:80\n127.0.0.1:80\nlocalhost\n匹配所有端口：*.test.com(:[0-9]+)?", "tip38": "最多{maxNum}个，使用换行符分隔，支持通配，支持 IP ，支持端口，如：\n*.test.com\ntest.com:80\n127.0.0.1:80\nlocalhost", "tip39": "最多{maxNum}个，使用换行符分隔，支持网段添加，如: 127.0.0.1/24", "tip40": "使用换行符分隔，支持网段添加，如: 127.0.0.1/24", "tip41": "最多支持400个，使用换行分隔，支持正则或通配，正则不支持*开头。您可根据所选匹配方式进行配置，如：\n通配符：\nwget表示精确匹配\n*wget*表示模糊匹配\n正则：\nwget 表示模糊匹配\n^wget$表示精确匹配", "tip41-1": "最多400个，使用换行符分隔，以/开头的URI，包含?及参数，不含http://及域名，支持正则表达式，如：\n匹配一级目录：^/aa/\n严格匹配，不带参数：^/aa/bbb/a\\.txt$\n可以带参数：^/aa/bbb/a\\.txt(\\?.*)?$", "tip42": "请选择类型", "tip43": "输入的地址大于{maxNum}条，请检查", "tip44": "输入的域名地址中有重复，请检查", "tip45": "请输入IP地址", "tip46": "输入的IP大于{maxNum}条，请检查", "tip47": "输入的IP地址格式不正确，请重新输入", "tip48": "输入的ip地址中有重复，请检查", "tip49": "请输入UA地址", "tip49-1": "请输入URL地址", "tip50": "输入的值大于{maxNum}条，请检查", "tip51": "请输入加密key", "tip52": "多个key以英文逗号分隔，最多支持3个", "tip53": "多个key以英文逗号分割，每个1~128个字符，不允许使用$ ； ， 、符号。", "tip54": "请输入正整数，最大支持8位。", "tip55": "支持文件类型：text/xml,text/plain,text/css,application/javascript,application/x-javascript,application/rss+xml,text/javascript,image/tiff,image/svg+xml,application/json,application/xml", "tip56": "文件大小支持(1-1023) B 或(1-1023) K 或(1-102399) M，例如1K。", "tip57": "请输入最小压缩文件", "tip58": "请输入正确的文件大小格式!", "tip59": "请选择压缩文件方式", "tip60": "请输入压缩文件类型", "tip61": "请选择跳转类型", "tip62": "请选择跳转方式", "tip63": "选项不是合法值", "tip63-1": "选项中有包含非法值", "tip64": "默认1，可配置范围1到5。", "tip65": "支持对回源请求的URI进行改写，可配置多条改写规则，支持正则表达式，从上到下按顺序依次执行可以匹配的所有规则。", "tip66": "以/开头的URI，不含http://头及域名。支持正则表达式，如^/test$。", "tip67": "以/开头的URI，不含http://头及域名。", "tip68": "该功能可以配置CDN回源到媒体存储的私有Bucket，使用子账号AK/SK，授予CDN对您的媒体存储Bucket的只读权限。仅当有配置媒体存储源站时有效。", "tip68-1": "该功能可以配置CDN回源到对象存储的私有Bucket，使用子账号AK/SK，授予CDN对您的对象存储Bucket的只读权限。仅当有配置对象存储源站时有效。", "tip69": "回源参数规则配置可实现添加，删除，保留，修改回源参数功能。", "tip70": "参数值为空代表删除对应参数。", "tip71": "保留指定参数示例：参数名:a，参数值：$arg_a，其中$arg_a代表问号后参数a的值。不支持参数名带中划线，如参数名：a-b，如需配置，请提交工单。", "tip72": "请选择动态回源策略", "tip73": "请输入源站", "tip74": "请选择层级", "tip75": "请选择回源协议", "tip76": "请输入请求头参数", "tip77": "该模块无法修改host请求头，如您需要修改回源host头部，可通过:回源配置-回源HOST进行修改。", "tip78": "取值不支持空格", "tip79": "请输入待改写PATH", "tip79-1": "请输入待改写PATH", "tip80": "请输入目标PATH", "tip80-1": "请输入改写后PATH", "tip81": "请输入AK", "tip82": "请输入SK", "tip83": "请输入参数名", "tip84": "参数仅支持大小写字母、数字", "tip85": "AK最多为{num}个字符", "tip86": "SK最多为{num}个字符", "tip87": "确定删除所选择的配置吗？", "tip88": "至少保留一行配置", "tip89": "输入的IP或源地址错误，请检查", "tip90": "请填写HTTP端口", "tip91": "请输入正确的端口号，1-65535，不支持下发443端口", "tip92": "请填写HTTPS端口", "tip93": "请输入正确的端口号，1-65535", "tip94": "取值  1-5", "tip95": "请输入数字", "tip96": "最多添加60条数据", "tip97": "管理回源HTTP请求头，可支持新增、修改、删除，可携带特定的HTTP Header参数信息给源站。", "tip98": "取值不支持中文文字及字符。", "tip99": "若需要添加/修改对应请求头，请输入取值；", "tip100": "若需要删除对应请求头，取值不填，置为空。", "tip101": "表单输入错误，请检查", "tip102": "媒体存储源站域名不符合条件，请重新输入", "tip103": "媒体存储源站域名只能配置一个", "tip104": "目前仅全站加速支持全球范围加速，请先变更加速区域。", "tip105": "3xx：301、302等（不支持304）", "placeholder1": "请输入文件类型，以 , 隔开", "placeholder2": "秒", "placeholder3": "支持多个，用逗号','分割", "placeholder4": "请输入响应头参数", "placeholder5": "请输入取值", "placeholder6": "请输入待改写PATH", "placeholder7": "请输入目标PATH", "placeholder8": "请输入状态码", "placeholder9": "请输入缓存后缀名", "placeholder9-1": "请输入后缀名", "placeholder10": "输入的后缀名格式不正确，请重新输入", "placeholder11": "请输入缓存目录", "placeholder11-1": "请输入目录", "placeholder12": "输入的目录格式不正确，请重新输入", "placeholder13": "请输入缓存全路径文件", "placeholder13-1": "请输入全路径文件", "placeholder14": "输入的全路径文件格式不正确，请重新输入", "placeholder15": "请选择忽略参数", "placeholder16": "请输入指定参数", "placeholder17": "取值不支持中文", "placeholder18": "请输入缓存内容", "placeholder19": "请设置正确的缓存时间", "placeholder20": "请输入权重", "placeholder21": "请输入整数，最大100，最小1", "placeholder22": "请输入优先级", "placeholder23": "多个以英文逗号分隔", "placeholder24": "请输入加密参数", "placeholder25": "请输入时间戳加密参数", "placeholder26": "多个类型用英文逗号分隔,若匹配所有类型填*", "placeholder27": "请输入指定源站回源HOST", "placeholder28": "默认为空字符串", "placeholder29": "请输入过期时间", "placeholder30": "取值范围为：0 到 31536000", "placeholder32": "只能输入1～100的整数", "placeholder33": "请确认是否提交保存下发配置，配置下发需要几分钟才可生效，配置中显示修改后的最终配置，不允许进行再次修改配置，请及时关注。", "placeholder34": "功能实现原理:边缘安全加速通过代理访问外链源站,在边缘云节点进行外链地址改写,并对外链提供IPv6加速,能够解决网站“天窗问题”。", "placeholder35": "点击确认，将代表您已详细了解天翼云外链改写服务及其相关原理，您对实施该服务相关方案的风险已知悉，同意接受天翼云外链改写服务并同意自行承担由此产生的一切责任。", "placeholder36": "若需要实现IPv6外链改造效果，请先在域名管理中开启IPv6解析能力。", "placeholder37": "开启后websocket协议支持服务端主动向客户端推送数据。开启后，默认连接超时时间5秒、请求超时时间15秒。", "placeholder38": "了解详情", "placeholder39": "该配置项不配置时，websocket回源连接超时时间同回源配置的回源连接超时时间，如果回源配置的回源连接超时时间也没配置时，底层默认5s。", "placeholder40": "请输入1-300之间的整数。", "placeholder41": "回源超时时间设置开关开启时，回源连接超时时间和回源请求超时时间必填一个。", "placeholder42": "请输入0-300之间的整数。", "placeholder43": "请精确填写协议与端口。", "placeholder44": "支持自助配置服务端口的套餐请参考{0}；加速区域是全球或全球（不含中国内地）时，不允许自助配置服务端口。若需要开通请提交{1}或者联系客服人员处理。", "placeholder44-0": "特殊端口请提交工单或联系专属技术支持处理。", "placeholder44-1": "版本差异对比", "placeholder45": "端口不能重复", "placeholder46": "HTTP端口和HTTPS端口不能重复", "placeholder47": "忽略指定参数示例：\"a,b\"，多个参数以英文逗号分隔。", "placeholder48": "全路径文件不能包含 ‘?’ ", "placeholder49": "全路径文件需要以 / 开头", "placeholder50": "请输入正确的后缀名，并用,隔开", "placeholder51": "请输入正确的目录，以/开始，并用,隔开", "placeholder52": "请输入首页", "placeholder53": "请输入全部文件", "placeholder54": "请输入内容", "placeholder55": "天", "placeholder56": "小时", "placeholder57": "分钟", "placeholder58": "请选择缓存规则", "placeholder59": "过期时间只能输入整数", "placeholder60": "单位为天，过期时间最大值为365", "placeholder61": "单位为时，过期时间最大值为8760", "placeholder62": "单位为分，过期时间最大值为525600", "placeholder63": "单位为秒，过期时间最大值为31536000", "placeholder64": "请选择去问号缓存", "placeholder65": "权重只能输入整数", "placeholder66": "权重最大值为100", "placeholder67": "权重最小值为1", "placeholder70": "按时间拖拉", "placeholder71": "按字节拖拉", "placeholder72": "请选择拖拉模式", "placeholder73": "请输入目录", "placeholder74": "请输入全路径文件", "placeholder75": "优先级只能输入整数", "placeholder76": "优先级最大值为100", "placeholder77": "优先级最小值为1", "placeholder78": "至少要有一条数据", "placeholder79": "上传加速和websocket不能同时开启。", "placeholder80": "请输入源站地址", "placeholder81": "多个端口以英文逗号分隔", "placeholder82": "服务端口与协议需精准匹配，填写时请注意准确性。", "placeholder83": "多个端口以逗号分隔，每个端口范围为1到65535", "placeholder84": "多个值之间用英文逗号分隔。", "tab1": "缓存key", "tab2": "基础配置", "tab3": "回源配置", "tab4": "配置", "tab5": "HTTPS配置", "tab6": "缓存配置", "tab6-1": "缓存过期时间", "tab7": "缓存过期时间", "tab8": "状态码过期时间", "tab9": "HTTP响应头", "tab10": "文件处理", "tab11": "业务脚本", "ttlTip": ["请输入缓存时间", "请正确设置过期时间", "请输入数字", "单位为天，过期时间最大值为1095", "单位为小时，过期时间最大值为26280", "单位为分钟，过期时间最大值为1576800", "单位为秒，过期时间最大值为94608000"], "cacheModeMap": ["后缀名", "目录", "首页", "全部文件", "全路径文件", "正则"], "trustlist": "白名单", "blocklist": "黑名单", "authType": ["鉴权A", "鉴权B", "鉴权C"], "jumpType": ["302跳转", "301跳转"], "originType": ["择优回源", "按权重回源", "保持登录"], "tip106": "4xx：400、401、403、404、405、407、414等", "tip107": "5xx：500、501、502、503、504、509、514等", "tip108": "静态加速关闭时默认全部文件不缓存，无法体验静态缓存加速能力。", "routeType": ["快速选路", "稳健选路", "应用层选路"], "tip109": "开放试用，开启后上传大小默认限制为：300M，建议叠加动态加速配置可使上传加速效果更佳。", "tip110": "保持参数顺序开关为 on，编码为 on 时，会把参数解码后，再编码。", "tip111": "请输入天翼云边缘安全加速平台产品提供的备源地址", "limitSpeed": {"tip1": "单请求限速规则", "tip2": "限速值", "tip3": "请输入限速值", "tip4": "最大限速值：100MB/s", "tip5": "最小限速值：1B/s", "tip6": "单请求限速"}, "ja3_fingerprint": "JA3指纹", "ja3_fingerprint_log": "JA3指纹记录", "ja3_fingerprint_tip": "开启JA3指纹记录，访问控制支持设置“JA3指纹”匹配字段，频率控制支持设置“JA3指纹”匹配字段及统计粒度。", "ja3_fingerprint_tip2": "开启JA3指纹识别开关后，为了确保功能能够正常生效并发挥其安全作用，您可以到 ", "ja3_fingerprint_tip2-1": "安全能力-访问控制/限流", "ja3_fingerprint_tip2-2": " 模块进行安全配置。", "rewriteMode": "改写模式", "rewriteModeTip": "默认解码，对回源uri解码后改写；选择编码，对回源uri原始编码改写。", "rewriteModeList": ["解码", "编码", "默认解码"]}, "batch": {"title": "批量修改域名", "title1": "确认批量修改域名", "title2": "修改配置信息", "title3": "完成", "label1": "HTTP请求头", "next": "下一步", "tip1": "是否确定批量修改域名配置", "tip2": "部分域名修改失败", "emptyReferer": "空Referer", "tip3": "输入的域名地址格式不正确，请重新输入", "tip4": "支持IP或域名，最多可添加60个。新增的源站内容将全量覆盖原有域名配置。", "以下根据勾选的功能进行覆盖下发": "以下根据勾选的功能进行覆盖下发", "请至少修改一项配置后继续操作": "请至少修改一项配置后继续操作"}, "dialog": {"label1": "域名：", "placeholder1": "请选择标签", "tip1": "已成功操作！"}, "editTip": {"tip1": "如您有需，可通过", "tip2": "提交工单", "tip3": "变更加速类型。"}, "editPage": {"btn1": "升级到基础版", "btn2": "编辑配置", "btn3": "取 消", "btn4": "保 存", "btn5": "放弃更改", "tip1": "域名当前配置中/已停用，不支持配置能力。域名状态为已启用方可编辑", "tip2": "回源连接超时时间默认值5s，回源请求超时时间默认值15s。", "tip3": "请先请求协议开启HTTPS后进行HTTPS相关配置", "tip4": "包含子域名如需配置为\"是\"，需先确认所有子域名都已开启HTTPS，否则子域名自动跳转到HTTPS将导致访问失败。", "tip5": "配置强制跳转功能，将客户端到边缘节点的原请求方式强制重定向为HTTP或者HTTPS请求", "tip6": "管理返回给客户端的HTTP Header信息，可新增、修改、删除", "tip7": "管理回源时携带的HTTP Header信息，可新增、修改、删除", "tip8": "文件压缩方式", "tip9": "压缩文件类型", "tip10": "开启URL鉴权功能后会对加密串及时间戳进行校验，有效保护站点资源。点击", "tip11": "边缘安全加速通过代理访问外链源站，在边缘云节点进行外链地址改写，并对外链提供IPv6加速，能够解决网站“天窗问题”。", "tip12": "HTML5定义 的websocket协议是基于TCP的一种新的网络协议，可有效节省服务器资源和带宽。 AccessOne基于丰富的边缘节点，叠加动态探测选路、智能调度，可有效提升实时通讯场景延时。", "tip13": "当前域名有未保存的配置，是否确定放弃改动？", "tip14": "当前域名正在新增中，页面展示初始化内容，请耐心等待5~10分钟", "tip15": "获取业务中心能力开关失败，请重新刷新页面。", "tip16": "域名配置中/已停用，不支持进行编辑", "tip17": "存在特殊配置，暂不支持自助修改。若需要调整请提交", "tip18": "或者联系客服人员处理。", "tip19-prev": "当前媒体存储源站仅支持国内区域，使用媒体存储作为源站可为您节省更多CDN回源流量费用。", "tip19": "使用{0}/{1}作为源站可为您节省更多CDN回源流量费用。", "tip19-1": "媒体存储", "tip19-2": "对象存储", "tip19-3": "1.媒体存储源站必须以{endPointStr}结尾，对象存储源站必须以{endPointStr2}结尾。媒体存储跟对象存储只能配置一种。", "tip19-4": "2.回媒体存储源/对象存储源需要同时将回源host修改成Bucket域名，请通过“指定源站回源host”功能进行配置。", "tip19-5": "3.一个加速域名里面不能同时配置媒体存储源站和对象存储源站，只能二选一。", "tip20": "确认删除该配置项吗？", "tip21": "暂不支持开通非标服务端口，若需要开通请提交", "tip22": "客服工单", "placeholder1": "支持正则表达式，以/开头的URI，不包含http://及域名。", "placeholder2": "以/开头的URL，不含http://头及域名。", "placeholder3": "仅支持大小写字母、数字、下划线、中划线输入", "placeholder4": "若要删除响应头则置空，输入不支持中文。", "placeholder5": "若要删除回源请求头则置空，输入不支持中文", "placeholder6": "支持文件类型如：text/xml,text/plain,text/css,application/javascript,application/x-javascript,application/rss+xml,text/javascript,image/tiff,image/svg+xml,application/json,application/xml", "placeholder7": "多个类型用英文逗号分隔,若匹配所有填*", "placeholder8": "请选择", "placeholder9": "请选择请求协议", "placeholder10": "以/开头的URI，包含?及参数，不含http://及域名，支持正则表达式，如(.+)", "placeholder11": "完整请求，包含协议，域名、?及参数，常用$1,$2来捕获pattern中圆括号内的字符串，例如$scheme://$host$1。$scheme表示客户请求协议，如http，https。$host表示当前加速域名", "placeholder13": "默认否", "placeholder14": "类型为空情况下，默认匹配所有文件", "label1": "基础信息", "label2": "产品类型", "label3": "回源类型", "label4": "回源端口", "label5": "回源超时时间设置", "label6": "回源连接超时时间", "label7": "回源请求超时时间", "label8": "回源URI改写", "label9": "改写后PATH", "label10": "增加规则", "label11": "添加回源参数规则", "label12": "请求协议", "label13": "包含子域名", "label14": "头部修改", "label15": "头部值", "label16": "IPv6外链改造", "label17": "是否允许空协议", "label18": "回源302/301跟随时生效", "label19": "证书", "label20": "添加源站", "label21": "网站首页IPv6标识", "label22": "IPv6标识内容", "label23": "IPv6标识显示时长", "label24": "是", "label25": "否", "label26": "服务端口", "label27": "静态配置", "label28": "动态配置", "label29": "上传配置", "label30": "页面优化", "label31": "视频拖拉", "label32": "跟随请求端口回源", "label33": "跨域验证", "label34": "高级配置", "label35": "跳转状态码", "label36": "访问URL重定向", "label37": "进制", "label38": "系数", "label39": "错误页面重定向", "label40": "错误状态码", "label41": "跳转页面", "radio1": ["HTTP回源", "HTTPS回源", "协议跟随"], "ipsetTip": "IP黑白名单集合优先于IP黑白名单执行", "certTip": "证书来源天翼云证书管理平台，若未找到证书。", "ipv6Tip": "您正在使用IPv6协议访问本网站", "domainFailTip": "域名信息获取失败，请重新刷新页面。", "websocketTip": "HTML5定义的websocket协议是基于TCP的一种新的网络协议，可有效节省服务器资源和带宽。 AccessOne基于丰富的边缘节点，叠加动态探测选路、智能调度，可有效提升实时通讯场景延时。", "tooltip1": "回源HTTP请求头不支持修改HOST请求头，若您需要修改回源HOST头部，可通过：回源配置-回源HOST 进行修改", "tooltip2": "使用IPv6访问时，开启IPv6访问标记，则在网页首页右下角显示“您正在使用IPv6协议访问本网站”，关闭后不再显示该标记。", "tooltip3": "IPv6标识内容最多支持输入50个字符", "tooltip4": "开启IPv6外链改写则将在边缘节点进行外链地址改写，提供IPv6外链加速能力，可提升IPv6支持度。解决网站“天窗问题”。", "tooltip5": "外链改造层数默认选择0，代表无限改写外链层数", "tooltip6": "支持仅对IPv6请求进行外链改造，也支持对IPv4与IPv6请求同时生效。", "tooltip7": "支持配置不需要生效外链改造的地址。请输入域名、uri、url，多个黑名单之间用逗号分隔，如：www.abc.com,/two_level.html", "tooltip8": "请输入域名、uri、url，多个黑名单之间用逗号分隔", "tooltip9": "支持对点击类的外部链接进行改造", "tooltip10": "当前域名有未保存的配置，是否在切换前保存配置？", "tooltip11": "", "tooltip12": "", "tooltip13": "", "tooltip14": "", "ruleTip1": "请输入头部值", "ruleTip2": "长度不超过300个字符", "ruleTip3": "参数仅支持大小写字母、数字、下划线、中划线", "ruleTip4": "请输入IPv6标识内容", "ruleTip5": "IPv6标识内容最多支持输入50个字符", "ruleTip6": "请输入IPv6标识显示时长", "ruleTip7": "只能输入正整数", "ruleTip8": "取值范围：1-2147483647", "ruleTip9": "请选择外链改写层数", "ruleTip10": "请选择改造生效范围", "ruleTip11": "支持的状态码：400、401、403、404、405、407、414、500、501、502、503、504、509、514", "ruleTip12": "过期时间只能输入整数", "ruleTip13": "单位为天，过期时间最大值为365", "ruleTip14": "单位为时，过期时间最大值为8760", "ruleTip15": "单位为分，过期时间最大值为525600", "ruleTip16": "单位为秒，过期时间最大值为31536000", "ruleIpset": "IP集名称不能为空", "dynamicTip": "提供动态请求加速服务，AceesOne通过实时检测全球节点延迟，通过智能探测选路、自研协议及内核优化技术，提升动态请求的访问速度。", "tooltip15": "开启动态加速后，在套餐基础上，客户端用户与边缘节点的动态请求数将被计费。", "tooltip16": "计费说明", "tip23": "收起基础配置", "tip24": "展开基础配置", "tip25": "提交失败，请检查", "tip26": "跟随请求端口回源开关开启后，使用请求端口回源", "tip27": "该加速域名不可变更源站相关配置。", "tip28": "该加速域名不可添加/变更content-type、content-disposition的自定义HTTP响应头配置。", "tip29": "不能添加{xosDefaultAccelerateSuffix}后缀的加速域名。", "tip30": "可填写值：300～599 且非499，多个以英文逗号分隔。", "tip31": "总长度最小为16位，须以http://或https://开头", "tip32": "相同错误状态码配置，仅优先级高的配置生效", "tip33": "全站加速-上传加速与全站加速收费不同，详见：", "tip34": "上传加速计费", "tip35": "，请确认是否修改。", "tip36": "全站加速-上传加速与全站加速收费不同，请确认是否修改。", "tip37": "全站加速-websocket加速与全站加速收费不同，详见：", "tip38": "websocket加速计费", "tip39": "全站加速-websocket加速与全站加速收费不同，请确认是否修改。", "tip40": "请输入错误状态码", "tip41": "请输入跳转页面", "tip42": "请输入跳转状态码", "tip43": "输入的跳转页面格式不正确，请重新输入", "tip44": "不能以“,”结尾", "tip45": "全站加速-上传加速与全站加速-websocket加速计费方式不同，详见：", "tip46": "全站加速-上传加速与全站加速-websocket加速计费方式不同，请确认是否修改。", "tip47": "全站加速-websocket加速与全站加速-上传加速计费方式不同，详见：", "tip48": "全站加速-websocket加速与全站加速-上传加速计费方式不同，请确认是否修改。", "tip49": "证书已过期或不存在，请选择证书。", "sni": {"tip1": "回源SNI", "tip2": "只能配置精确域名，不支持泛域名", "tip3": "请输入SNI", "tip4": "回源协议为https时才生效", "tip5": "输入的域名格式不正确"}, "split": {"tip1": "分片回源", "tip2": "关闭", "tip3": "开启", "tip4": "自适应", "tip5": "分片回源，是指CDN节点收到用户请求后，会在回源时携带Range请求头，源站在收到Range请求后，会返回对应范围的内容数据给CDN。", "tip6": "请点击{0}", "tip6-1": "了解更多", "tip7": "1. 开启：无论客户端发起的是否Range请求，CDN节点无缓存时，均按配置的Range分片大小回源。", "tip8": "2. 自适应：如客户端携带Range请求头，则按分片回源；如客户端没有携带Range请求头，则按完整文件回源。"}, "specialPortTip2": {"tip1": "当前域名使用资源有特殊约束，不支持自助配置非标服务端口，若需要配置请提交{0}或者联系客服人员处理。", "tip2": "客服工单"}, "originPortTip": {"tip1": "跟随请求端口回源优先生效。", "tip2": "跟随请求端口回源优先生效，开关开启后，使用请求端口回源"}}, "changeAcceArea": {"title": "变更加速区域", "ctcloud": {"tip1": "1、不同加速区域对应不同资费标准，加速区域选择“中国内地”或“全球”时：", "tip1-1": "1）如您的账号还未完成实名认证，请先通过指引文档：<a target='_blank' href={link} class='link'>实名认证</a>，完成实名认证。完成账号实名认证后，请重新登录账号后再试。", "tip1-2": "2）如您的加速域名尚未完成中国大陆的ICP备案，请先完成在中国大陆的ICP备案。", "tip2": "2、修改加速区域，由于服务节点变更，短期内回源的流量会增加，命中率会下降，请您留意源站运行情况。", "tip3": "3、修改加速区域可能会引起回源IP变更，如果您的源站有回源IP白名单限制，请通过<a target='_blank' href={link} class='link'>【客户服务工单】</a>提交接收回源白名单邮箱（支持多个邮箱），收到天翼云CDN+团队发送的回源白名单邮件并在源站加白完成后，再到客户控制台变更加速区域。"}, "ctyun": {"tip1": "1、不同加速区域对应不同资费标准，加速区域包含中国内地时，域名请先完成在中国大陆的ICP备案，同时完成公安网的备案。", "tip2": "2、修改加速区域，由于服务节点变更，短期内回源的流量会增加，命中率会下降，请您留意源站运行情况。", "tip3": "3、修改加速区域可能会引起回源IP变更，如果您的源站有回源IP白名单限制，请通过<a target='_blank' href={link} class='link'>【客户服务工单】</a>提交接收回源白名单邮箱（支持多个邮箱），收到天翼云CDN+团队发送的回源白名单邮件并在源站加白完成后，再到客户控制台变更加速区域。"}, "tip4": "提交变更加速区域工单成功！", "tip5": "加速区域变更", "tip6": "如您有需，可点击变更加速类型。"}, "ipv6": {"tip1": "关闭后将不支持IPv6解析，请确认是否关闭？", "tip2": "请确认是否需要支持IPv6解析？", "tip3": "IPv6解析开关下发成功，等待一段时间之后生效"}, "htmlForbid": {"forbid1": "html禁止操作", "forbid2": "禁止复制", "forbid3": "禁止右键", "forbid4": "请输入后缀名", "forbid5": "类型和内容需要同时配置", "forbid6": "“禁止复制”和“禁止右键“至少开启一项"}, "changeDomainType": "全站加速-上传加速、全站加速-websocket加速仅支持中国内地加速，如需将域名类型变更为这两种类型，请先变更加速区域为“中国内地”。", "websocketTip": "websocket功能已关闭", "info": "消息", "httpsSupport": {"tip1": "您尚未开通HTTPS功能对应的服务，请{0}后再开启使用。", "tip2": "完成订购", "tip3": "域名对应产品类型尚未开通HTTPS功能对应的服务"}, "entryLimit": {"tips": "当单IP单域名每秒访问单台服务器的次数达到了设置的阈值，返回403状态码，阻断时间默认10分钟。", "IP访问限频": "IP访问限频", "访问阈值": "访问阈值", "IP访问限频规则": "IP访问限频规则", "次/秒": "次/秒", "请输入访问阈值": "请输入访问阈值", "访问阈值取值范围": "取值范围为：{0} 到 {1}"}, "您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。": "您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。", "需配置证书才能开启QUIC功能，默认支持版本：IETF-QUIC(H3-v1)": "需配置证书才能开启QUIC功能，默认支持版本：IETF-QUIC(H3-v1)", "如果域名存在指定源站回源host配置时，则不对此域名更新默认回源host。": "如果域名存在指定源站回源host配置时，则不对此域名更新默认回源host。", "sharedHost": {"title": "共享缓存", "cacheName": "缓存域名", "selectCacheName": "请选择缓存域名", "tips": "配置共享缓存会导致缓存key变更，一旦被共享缓存的域名无缓存，可能造成回源突增，请谨慎配置！"}, "remoteAuth": {"远程同步鉴权": "远程同步鉴权", "鉴权源站": "鉴权源站", "基础信息": "基础信息", "鉴权请求uri": "鉴权请求uri", "请求配置": "请求配置", "请求协议": "请求协议", "请求端口": "请求端口", "请求方法": "请求方法", "鉴权请求参数": "鉴权请求参数", "保留参数设置": "保留参数设置", "保留所有参数": "保留所有参数", "删除所有参数": "删除所有参数", "自定义参数": "自定义参数", "自定义": "自定义", "参数名": "参数名", "参数值": "参数值", "增加参数": "增加参数", "参数配置": "参数配置", "参数是否区分大小写": "参数是否区分大小写", "参数是否编码": "参数是否编码", "鉴权请求头": "鉴权请求头", "保留请求头设置": "保留请求头设置", "保留所有请求头": "保留所有请求头", "删除所有请求头": "删除所有请求头", "自定义请求头": "自定义请求头", "请求头名称": "请求头名称", "请求头值": "请求头值", "增加请求头": "增加请求头", "基于状态码鉴权": "基于状态码鉴权", "鉴权成功状态码": "鉴权成功状态码", "状态码": "状态码", "基于响应body鉴权": "基于响应body鉴权", "响应body": "响应body", "鉴权失败时响应状态码": "鉴权失败时响应状态码", "固定状态码": "固定状态码", "跟随鉴权源站": "跟随鉴权源站", "鉴权超时设置": "鉴权超时设置", "鉴权超时后动作": "鉴权超时后动作", "通过": "通过", "拒绝": "拒绝", "添加鉴权设置": "添加鉴权设置", "ip/域名，多个逗号分隔": "ip/域名，多个逗号分隔", "请输入参数名": "请输入参数名", "请输入参数值": "请输入参数值", "请输入请求头名称": "请输入请求头名称", "请输入请求头值": "请输入请求头值", "请输入鉴权源站": "请输入鉴权源站", "格式:/auth 或 $uri": "格式:/auth 或 $uri", "请输入鉴权请求uri": "请输入鉴权请求uri", "选择参数": "选择参数", "鉴权状态码类型": "鉴权状态码类型", "鉴权失败状态码": "鉴权失败状态码", "鉴权服务器根据鉴权结果返回给CDN的HTTP状态码。例如：配置鉴权成功状态码：200，则代表鉴权服务器返回200时鉴权通过，其他状态码均不通过；配置鉴权失败状态码：403，则代表鉴权服务器返回403时鉴权不通过，其他状态码均通过。": "鉴权服务器根据鉴权结果返回给CDN的HTTP状态码。例如：配置鉴权成功状态码：200，则代表鉴权服务器返回200时鉴权通过，其他状态码均不通过；配置鉴权失败状态码：403，则代表鉴权服务器返回403时鉴权不通过，其他状态码均通过。", "响应状态码类型": "响应状态码类型", "添加鉴权配置": "添加鉴权配置", "秒": "秒", "鉴权超时时间": "鉴权超时时间", "状态码鉴权通过时，如果有开启“基于响应body鉴权”，还会结合响应body的内容最终判断是否鉴权通过。": "状态码鉴权通过时，如果有开启“基于响应body鉴权”，还会结合响应body的内容最终判断是否鉴权通过。", "输入响应body内容": "输入响应body内容", "选择请求头": "选择请求头", "有值代表添加/修改，值为空代表删除。": "有值代表添加/修改，值为空代表删除。", "请选择保留参数设置": "请选择保留参数设置", "请输入正确的状态码": "请输入正确的状态码", "请选择响应状态码类型": "请选择响应状态码类型", "请输入响应body内容": "请输入响应body内容", "请选择鉴权状态码类型": "请选择鉴权状态码类型", "请输入正确的超时时间": "请输入正确的超时时间", "鉴权超时时间取值范围为：0 到 3600": "鉴权超时时间取值范围为：0 到 3600", "小数位数最大3位": "小数位数最大3位", "不能包含空值": "不能包含空值", "默认3": "默认3", "默认403": "默认403", "默认80": "默认80", "请选择或输入参数名": "请选择或输入参数名", "请选择或输入参数值": "请选择或输入参数值", "请选择或输入请求头名称": "请选择或输入请求头名称", "请选择或输入请求头值": "请选择或输入请求头值", "默认http": "默认http", "格式错误": "格式错误", "不能包含空字符串": "不能包含空字符串", "默认GET": "默认GET", "默认443": "默认443", "请求体": "请求体", "默认是": "默认是", "多个状态码用逗号分隔": "多个状态码用逗号分隔"}, "请选择指定源站回源HOST": "请选择指定源站回源HOST", "默认回源HOST": "默认回源HOST", "请选择默认回源HOST": "请选择默认回源HOST", "请输入默认回源HOST": "请输入默认回源HOST", "源站域名": "源站域名", "自定义域名": "自定义域名", "TLS版本至少包含国际TLS协议中的一种且至少包含国密TLS协议中的一种": "TLS版本至少包含国际TLS协议中的一种且至少包含国密TLS协议中的一种", "加密套件至少包含国际加密套件中的一种且至少包含国密加密套件中的一种": "加密套件至少包含国际加密套件中的一种且至少包含国密加密套件中的一种"}