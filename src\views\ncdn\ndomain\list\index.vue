<template>
    <ct-section-wrap>
        <template #header>
            <!--  :tip="$t('domain.list.titleTip')" -->
            <ct-section-header :title="$t('domain.list.title')"></ct-section-header>
        </template>
        <ct-box class="table-scroll-wrap">
            <div class="search-panel-wrap">
                <div class="search-bar">
                    <label-select
                        :labelList="labelList"
                        v-model="selectedLableValueList"
                        :key="timer"
                    ></label-select>
                    <el-select v-model="product">
                        <el-option :label="$t('domain.list.allTypes')" value="0" />
                        <el-option
                            v-for="opt in ProductOptions"
                            :key="opt.value"
                            :label="$t(`${opt.label}`)"
                            :value="opt.value"
                        />
                    </el-select>
                    <el-select v-model="status">
                        <el-option
                            v-for="opt in StatusOptions"
                            :key="opt.value"
                            :label="opt.label"
                            :value="opt.value"
                        />
                    </el-select>
                    <el-input :placeholder="$t('domain.list.placeholder1')" v-model="searchVal" />
                </div>
                <div class="search-btns">
                    <el-button type="primary" @click="refresh()">{{ $t("common.search.start") }}</el-button>
                    <el-button @click="refresh('reset')">{{ $t("domain.list.reset") }}</el-button>
                </div>
            </div>
            <el-divider></el-divider>
            <div class="table-button-wrapper">
                <div>
                    <el-button type="primary" icon="el-icon-plus" @click="judgeBeforeNew">
                        {{ $t("domain.list.createBtn") }}
                    </el-button>
                    <el-button
                        v-if="batchCreate"
                        type="primary"
                        icon="el-icon-plus"
                        @click="judgeBeforeNew('batch')"
                        >{{ $t("domain.list.batchCreateBtn") }}</el-button
                    >
                    <el-button type="primary" v-if="batchUpdate" @click="batchEdit">
                        {{ $t("domain.list.batchEditBtn") }}
                    </el-button>
                </div>
                <el-button type="text" @click="download" :disabled="loading">
                    <i class="el-alert__icon cute-icon-export"></i>
                    <ct-svg-icon icon-class="export" />
                    {{ $t("domain.list.export") }}
                </el-button>
            </div>

            <el-table
                :empty-text="$t('common.table.empty')"
                :data="showList"
                v-loading="loading"
                @selection-change="handleSelectionChange"
                :row-key="row => row.domain"
                ref="domainTable"
            >
                <el-table-column
                    v-if="batchUpdate"
                    type="selection"
                    width="50"
                    :selectable="tableSelectable"
                    :reserve-selection="true"
                >
                </el-table-column>
                <el-table-column prop="index" :label="$t('domain.list.tableLabel1')" width="60" />
                <el-table-column prop="domain" :label="$t('domain.list.tableLabel2')" minWidth="150">
                    <template slot-scope="{ row }">
                        {{ row.domain }}
                    </template>
                </el-table-column>
                <el-table-column prop="cname" label="CNAME" minWidth="130">
                    <template v-slot:header>
                        CNAME
                        <el-tooltip placement="right">
                            <div slot="content">
                                {{ $t("domain.list.tableTip1") }}

                                <el-link
                                    type="primary"
                                    class="aocdn-ignore-link"
                                    v-if="cnameHelpUrl"
                                    @click="$docHelp(cnameHelpUrl)"
                                >
                                    {{ $t("domain.list.tableTip2") }}
                                </el-link>
                            </div>
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <template slot-scope="scope">
                        {{ scope.row.cname }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('domain.list.tableLabel3')" minWidth="130">
                    <template slot-scope="{ row }">
                        <div class="list-cell-wrapper">
                            <div>{{ productFormatter(row) }}</div>
                            <el-tooltip placement="right" effect="dark">
                                <div slot="content">
                                    {{ $t("domain.changeAcceArea.tip6") }}
                                    <a class="aocdn-ignore-link" @click="$docHelp(acceAreaTipLearnMore)">{{
                                        $t("domain.detail.tip23")
                                    }}</a>
                                </div>
                                <i
                                    v-if="canChangeAccelerationType(row)"
                                    class="el-icon-refresh list-cell-wrapper-icon"
                                    @click="changeAccelerationType(row)"
                                ></i>
                            </el-tooltip>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('domain.list.tableLabel4')" minWidth="150">
                    <template slot-scope="{ row }">
                        <div class="list-cell-wrapper">
                            <div>{{ areaFormatter(row) }}</div>
                            <el-tooltip
                                :content="$t('domain.changeAcceArea.tip5')"
                                placement="right"
                                effect="dark"
                            >
                                <i
                                    v-if="canChangeAccelerationArea(row)"
                                    class="el-icon-refresh list-cell-wrapper-icon"
                                    @click="changeAccelerationArea(row)"
                                ></i>
                            </el-tooltip>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column width="120">
                    <template v-slot:header>
                        {{ $t("domain.list.tableLabelipv6") }}
                        <el-tooltip
                            placement="right"
                            :content="$t('domain.list.tableLabelipv6Tooltip')"
                            effect="dark"
                        >
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <template slot-scope="{ row }">
                        <el-switch
                            :disabled="canChangeIpv6(row)"
                            :value="row.ipv6_switch"
                            @change="val => ipv6_enable_change(row, val)"
                            :active-value="1"
                            :inactive-value="0"
                        />
                    </template>
                </el-table-column>
                <!-- 高性能网络 -->
                <el-table-column
                    width="190"
                    v-if="showCDNPremiumNetworkSwitch || showWholeStationPremiumNetworkSwitch"
                >
                    <template v-slot:header>
                        {{ $t("domain.list.tableLabelPremiumNetwork") }}
                        <el-tooltip placement="right" effect="dark">
                            <div slot="content">
                                <div>{{ $t("domain.list.tableLabelPremiumNetworkTip1") }}</div>
                                <div>{{ $t("domain.list.tableLabelPremiumNetworkTip2") }}</div>
                                <div>{{ $t("domain.list.tableLabelPremiumNetworkTip3") }}</div>
                            </div>
                            <i class="el-icon-question"></i>
                        </el-tooltip>
                    </template>
                    <template slot-scope="{ row }">
                        <el-switch
                            v-if="
                                (row.productCode === '008' && showCDNPremiumNetworkSwitch) ||
                                    (row.productCode === '006' &&
                                        ['006', null, ''].includes(row.subProductCode) &&
                                        showWholeStationPremiumNetworkSwitch)
                            "
                            :disabled="canChangePremiumNetwork(row)"
                            :value="row.virtual_config && row.virtual_config.virtual_status"
                            @change="val => premium_network_change(row, val)"
                            :active-value="1"
                            :inactive-value="0"
                        />
                        <span
                            v-if="row.virtual_config && row.virtual_config.virtual_scene"
                            class="scene-text"
                            >{{ virtualSdceneFormatter(row) }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column :label="$t('domain.list.tableLabel5')" :minWidth="isEn ? 150 : 110">
                    <template slot-scope="scope">
                        <cute-state :color="statusColorMap[scope.row.status]">
                            {{ $t(`${DomainStatusMap[scope.row.status]}`) }}
                            <span v-if="scope.row.status === 3 && DomainActionsMap[scope.row.action]">
                                ({{ $t(`${DomainActionsMap[scope.row.action]}`) }})
                            </span>
                        </cute-state>
                    </template>
                </el-table-column>
                <!-- 优先修改时间，其次开始时间 -->
                <el-table-column
                    :label="$t('domain.list.tableLabel6')"
                    prop="insertDate"
                    minWidth="150"
                    :formatter="timeFormatter"
                />
                <!-- 增加标签列 -->
                <el-table-column :label="$t('domain.list.tableLabel7')" minWidth="60">
                    <template slot-scope="scope">
                        <el-button
                            v-if="
                                domainMapLabel[scope.row.domain] &&
                                    domainMapLabel[scope.row.domain].length > 0
                            "
                            type="text"
                            icon="el-icon-price-tag"
                            @click="addLabel(scope.row)"
                            class="label-icon"
                        ></el-button>
                        <el-button
                            v-else
                            type="text"
                            @click="addLabel(scope.row)"
                            :disabled="scope.row.creating && scope.row.status === 3"
                            >{{ $t("domain.list.addLabel") }}</el-button
                        >
                    </template>
                </el-table-column>

                <!-- 由于后续要把视频直播加速从CDN控制台迁移出去 -->
                <!-- 暂时先屏蔽视频直播的编辑、其他操作，只保留查看 -->
                <el-table-column :label="$t('domain.operate')" minWidth="190">
                    <template #default="{ row }">
                        <!-- 已启用4：查看/编辑/停用
                            已停止6：查看/启用/删除
                            配置中3：查看 -->
                        <!-- 按钮分三类：查看、编辑、其他 -->
                        <el-button type="text" @click="onOperation(row, 'view')">
                            {{ $t("domain.view") }}
                        </el-button>
                        <el-button
                            v-if="row.status == 4 && row.productCode !== '005'"
                            type="text"
                            @click="onOperation(row, 'edit')"
                        >
                            {{ $t("domain.edit") }}
                        </el-button>
                        <el-button v-if="row.status == 6" type="text" @click="beforeUpdateDomain(row, 3)">
                            {{ $t("domain.enable") }}
                        </el-button>
                        <el-dropdown v-if="row.productCode !== '005' && (row.status == 6 || row.status == 4)">
                            <el-button type="text"
                                >{{ $t("domain.more") }}<i class="el-icon-arrow-down el-icon--right"></i
                            ></el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native="beforeUpdateDomain(row, 2)">
                                    <el-button v-if="row.status == 4" type="text">
                                        {{ $t("domain.disable") }}
                                    </el-button>
                                </el-dropdown-item>
                                <el-dropdown-item @click.native="beforeUpdateDomain(row, 1)">
                                    <el-button v-if="row.status == 6" type="text">
                                        {{ $t("domain.delete") }}
                                    </el-button>
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>

            <label-dialog
                :domainItem="domainItem"
                :labelVisible="labelVisible"
                @cancel="cancel"
            ></label-dialog>

            <div class="ist-pager">
                <el-pagination
                    :small="isXs"
                    :layout="isXs ? 'prev, pager, next' : 'total, sizes, prev, pager, next'"
                    :total="filterList.length"
                    :current-page.sync="page"
                    :page-size="pageSize"
                    :page-sizes="[10, 30, 50, 100]"
                    :hide-on-single-page="false"
                    @size-change="sizeChange"
                />
            </div>
        </ct-box>
        <changeAcceAreaDialog
            :visible="currentUpdateAreaDomain.visible"
            :row="currentUpdateAreaDomain.row"
            :changeAccelerationType="changeAccelerationType"
            @close="currentUpdateAreaDomain.visible = false"
            @closeWithRefresh="acceAreaDialogCloseWithRefresh"
        ></changeAcceAreaDialog>
        <change-acce-type-dialog
            :visible="showAcceTypeChangeDailog"
            :data="acceTypeChangeTableData"
            :submitCallback="acceTypeChangeCallback"
            @close="showAcceTypeChangeDailog = false"
        />
        <premiumNetworkSceneDialog
            :dialogVisible="sceneDialogVisible"
            :rowData="rowData"
            :updateLoading="updateLoading"
            @cancel="premiumNetworkSceneCancel"
            @submit="premiumNetworkSceneSubmit"
            @virtualSceneChange="virtualSceneChange"
        />
        <!-- 域名个数限制-弹窗 -->
        <domainCountLimitDialog
            :dialogVisible="domainCountLimitDialogVisible"
            :domainCountLimitLink="domainCountLimitLink"
            @submit="domainCountLimitSubmit"
        ></domainCountLimitDialog>
    </ct-section-wrap>
</template>
<script lang="ts">
import { Component, Watch, Vue } from "vue-property-decorator";
import { ProductModule } from "@/store/modules/ncdn/nproduct";
import { getI18nLabel } from "@/store/modules/ncdn/nproduct";
import { nUserModule } from "@/store/modules/nuser";
import { ScreenModule } from "@/store/modules/screen";

import { delayedResponse, downloadCsv, errorHandler } from "@/utils";
import { timeFormat } from "@/filters";
import { nDomainUrl, nBasicConfig, getRatioAndScale } from "@/config/url";

import { MessageType } from "element-ui/types/message";

import { DomainItem } from "@/types/domain";
import {
    ProductCodeEnum,
    DomainStatusMap,
    DomainActionsMap,
    GetCtiamButtonAction,
    CtiamButtonEnum,
} from "@/config/map";

import LabelSelect from "@/components/lableSelect/index.vue";
import LabelDialog from "./components/LabelDialog.vue";
import { LabelModule } from "@/store/modules/label";
import { NEW_PREFIX } from "@/config/url/_PREFIX";
import { ElTable } from "element-ui/types/table";
import { DomainModule } from "@/store/modules/domain";
import variables from "@cutedesign/ui/style/themes/default/index.scss";
import i18n from "@/i18n";
import changeAcceAreaDialog from "./components/changeAcceArea.vue";
import ChangeAcceTypeDialog from "./components/ChangeAcceType.vue";
import "isomorphic-fetch";
import { StatisticsModule } from "@/store/modules/statistics";
import premiumNetworkSceneDialog from "@/views/ncdn/ndomain/list/components/premiumNetworkSceneDialog.vue";
import { domainEndsWithSpecialSuffix } from "@/utils/product";
import { checkCtiamButtonAuth } from "@/store/modules/menu";
import ctSvgIcon from "@/components/ctSvgIcon/index.vue";
import { CtiamCode } from "@/utils/ctFetch/errorConfig";
import domainCountLimitDialog from "@/views/ncdn/ndomain/list/components/domainCountLimitDialog.vue";
import { CODE_QUOTA_EXCEEDED_2396 } from "@/utils/ctFetch/errorConfig";

type DomainChangeCache = {
    d: string; // 域名
    D: number; // 时间戳
};

const statusColorMap = {
    1: variables.colorMaster,
    3: variables.colorMaster,
    2: variables.colorSuccess,
    4: variables.colorSuccess,
    5: variables.colorDanger,
    7: variables.colorDanger,
    9: variables.colorDanger,
    10: variables.colorDanger,
    11: variables.colorDanger,
    12: variables.colorDanger,
    6: variables.colorInfo,
    8: variables.colorInfo,
};
// 域名校验接口
const domainValidateUrl = NEW_PREFIX + "/domain/validate";

@Component({
    name: "List",
    components: {
        LabelSelect,
        LabelDialog,
        changeAcceAreaDialog,
        ChangeAcceTypeDialog,
        premiumNetworkSceneDialog,
        ctSvgIcon,
        domainCountLimitDialog,
    },
})
export default class DomainList extends Vue {
    private domainCountLimit = 20; // 批量修改的域名的数量限制
    private selectedDomains: DomainItem[] = []; // 勾选的域名
    // 选中的标签的value列表，默认不选标签
    private selectedLableValueList: string[] = [];
    private timer = new Date().getTime();
    private page = 1;
    private pageSize = 10;
    private loading = false;
    private dataList: DomainItem[] = []; // 接口查询的完整结果
    private searchVal = ""; // 搜索条件：关键字
    private status = "0"; // 搜索条件：域名状态
    private product = "0"; // 搜索条件：加速类型
    private cnameHelpUrl = ""; //配置指引
    private batchCreate = false;
    private currentUpdateAreaDomain = {
        row: {},
        visible: false,
    };
    private searchFetchController: any = null;
    private acceTypeChangeCallback: any = null;
    private acceTypeChangeTableData: any = [];
    private showAcceTypeChangeDailog = false;

    private StatusOptions = [
        { label: this.$t("domain.list.status[0]"), value: "0" },
        { label: this.$t("domain.list.status[1]"), value: "4" },
        { label: this.$t("domain.list.status[2]"), value: "6" },
        { label: this.$t("domain.list.status[3]"), value: "3" },
    ];
    private DomainStatusMap = DomainStatusMap;
    private DomainActionsMap = DomainActionsMap;
    private localDomainChangeCache: DomainChangeCache[] = []; // 缓存变更记录
    private statusColorMap = statusColorMap;

    // private disableFormVisible = false;
    // private disableForm = {
    //     domain: "",
    //     reason: "",
    // };
    private rowItem: any = {};

    private labelVisible = false;

    private domainItem = {};

    private sceneDialogVisible = false; // 高性能网络弹窗
    rowData: any = {};
    virtual_scene: any = null;
    updateLoading = false;
    premium_network_switch: any = null;
    // 域名个数限制-链接
    private domainCountLimitLink = "";
    private domainCountLimitDialogVisible = false;

    // 计算属性
    get DomainAction(): { [key: number]: string } {
        return {
            1: `${i18n.t("domain.delete")}`,
            2: `${i18n.t("domain.disable")}`,
            3: `${i18n.t("domain.enable")}`,
        };
    }
    get labelList() {
        return LabelModule.labelList;
    }
    set labelList(val) {
        this.labelList = val;
    }
    get domainMapLabel() {
        return LabelModule.domainMapLabelJr;
    }
    get UserInfo() {
        return nUserModule.userInfo;
    }
    get isCtclouds() {
        return nUserModule.isCtclouds;
    }
    get ProductOptions() {
        //二级产品过滤掉
        const productCodeArr: string[] = Object.values(ProductCodeEnum);
        return ProductModule.productOptions.filter(item => {
            return (
                item.value !== ProductCodeEnum.Socket &&
                item.value !== ProductCodeEnum.Upload &&
                productCodeArr.includes(item.value)
            );
        });
    }
    get ProductList() {
        return ProductModule.list;
    }
    get canUseList() {
        return ProductModule.canUseList;
    }
    // CDN加速有开通高性能网络
    get showCDNPremiumNetworkSwitch() {
        return ProductModule.showCDNPremiumNetworkSwitch;
    }
    // 全站加速有开通高性能网络
    get showWholeStationPremiumNetworkSwitch() {
        return ProductModule.showWholeStationPremiumNetworkSwitch;
    }
    // get toolbarOperation() {
    //     console.log(OperationModule["domain.toolbar"]["000"]);
    //     return OperationModule[OperationScopeEnum.DomainToolbar][ProductCodeEnum.Basic];
    // }
    // 是否有批量修改的权限
    // 没有批量修改的权限时，不展示域名列表的复选框
    // get canBatch() {
    //     return this.toolbarOperation.some(item => {
    //         return item.ucode === "batch.prejudge";
    //     });
    // }
    get isXs() {
        return ScreenModule.width < 600;
    }

    get lang() {
        return nUserModule.lang;
    }

    get isEn() {
        return nUserModule.lang === "en";
    }
    get xosDefaultAccelerateSuffix() {
        return StatisticsModule.xosDefaultAccelerateSuffix;
    }
    get zosDefaultAccelerateSuffix() {
        return StatisticsModule.zosDefaultAccelerateSuffix;
    }

    private beforeMount() {
        this.checkBatcCreate();
        this.search();
        this.cnameHelpUrl = this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/document/zh-cn/10015932/10028579"
                : "https://www.esurfingcloud.com/document/10015932/10028579"
            : "https://www.ctyun.cn/document/10015932/10028579";
    }

    private tableSelectable(row: DomainItem) {
        const isSpecificSuffix = domainEndsWithSpecialSuffix(row.domain, [
            this.xosDefaultAccelerateSuffix,
            this.zosDefaultAccelerateSuffix,
        ]);

        return row.productCode !== "005" && !isSpecificSuffix;
    }

    // 批量修改域名
    async batchEdit() {
        DomainModule.nGetDomainAllBatchIcon();
        // 未选择域名
        if (this.selectedDomains.length === 0) {
            this.$message.error(
                `${this.$t("domain.list.tip4", { domainCountLimit: this.domainCountLimit })}`
            );
            return;
        }

        // 选择了除静态加速和视频点播加速之外的加速类型
        // if (
        //     this.selectedDomains.find(item => {
        //         return !["001", "003", "004", "006", "008", "014"].includes(item.productCode);
        //     })
        // ) {
        //     // TODO: 修改提示
        //     this.$message.error(`${this.$t("domain.list.tip5")}`);
        //     return;
        // }

        // 已启用的域名才可编辑，status 为 4
        if (
            this.selectedDomains.find(item => {
                return !(item.status === 4 && item.productCode !== "005");
            })
        ) {
            this.$message.error(`${this.$t("domain.list.tip7")}`);
            return;
        }

        // 选择的域名数量大于domainCountLimit
        if (this.selectedDomains.length > this.domainCountLimit) {
            this.$message.error(
                `${this.$t("domain.list.tip8", { domainCountLimit: this.domainCountLimit })}`
            );
            return;
        }
        // 批量修改，不能添加.ctyuncs.cn后缀的域名
        if (this.xosDefaultAccelerateSuffix || this.zosDefaultAccelerateSuffix) {
            const domainArr = this.selectedDomains?.map(item => item.domain);
            const data = domainArr.some(e =>
                domainEndsWithSpecialSuffix(e, [
                    this.xosDefaultAccelerateSuffix,
                    this.zosDefaultAccelerateSuffix,
                ])
            );
            if (data) {
                let suffix = "";
                [this.xosDefaultAccelerateSuffix, this.zosDefaultAccelerateSuffix].forEach(e => {
                    if (!e) return;
                    if (suffix) suffix += `,`;
                    suffix += `${e}`;
                });
                this.$message.error(
                    `${this.$t("domain.editPage.tip29", { xosDefaultAccelerateSuffix: suffix })}`
                );
                return;
            }
        }
        await checkCtiamButtonAuth(
            GetCtiamButtonAction("domainBatchModify"),
            this.selectedDomains.map(v => v.domain).join(",")
        );
        window.sessionStorage.setItem("batchEdit", JSON.stringify(this.selectedDomains));
        this.$router.push({
            name: "ndomain.batchEdit",
        });
    }

    // 当选择项发生变化时
    private handleSelectionChange(val: DomainItem[]) {
        this.selectedDomains = val;
    }

    // 将一个域名的数据转化为批量修改域名列表需要的格式
    // private transformDomainList(domainList) {}
    // 读 sessionStorage 中的修改记录
    private getDomainChangeCache() {
        this.localDomainChangeCache = JSON.parse(sessionStorage.getItem("domain-change-cache") || "[]");

        this.updateDomainChangeCache();
    }

    // 写 sessionStorage 中的修改记录
    private setDomainChangeCache(cache: DomainChangeCache) {
        this.localDomainChangeCache.push(cache);
        this.updateDomainChangeCache();
        sessionStorage.setItem("domain-change-cache", JSON.stringify(this.localDomainChangeCache));
    }

    // 更新域名变更缓存，剔除已失效部分（只锁存 10s ，避免刷新操作时，域名本身的状态更新被覆盖）
    private updateDomainChangeCache() {
        const CACHE_LIFE = 10; // 10 秒
        const nowD = +new Date();
        this.localDomainChangeCache = this.localDomainChangeCache.filter(
            (item: DomainChangeCache) => nowD - item.D < CACHE_LIFE * 1000
        );
    }

    // 查询
    private search() {
        this.searchFetchController && this.searchFetchController.abort("cancel by user"); // 取消上次请求
        this.searchFetchController = new AbortController(); // 建立一个controller方便控制本次请求的取消
        setTimeout(this.getDomainList); // 保证上一次请求取消后再进行下一次请求
    }

    private async getDomainList() {
        const signal = this.searchFetchController.signal; // 拿取controller中signal对象
        this.loading = true;
        try {
            const rst = await this.$ctFetch<{
                domainCountLimit?: number;
                list?: DomainItem[];
            }>(nDomainUrl.domainList, {
                data: {
                    pageIndex: 1,
                    pageSize: 1000,
                },
                signal, // 将本次请求的signal传入fetch
            });
            this.domainCountLimit = rst.domainCountLimit || 20;

            this.getDomainChangeCache();

            const list = rst.list || [];
            this.dataList = list
                .filter(item => item.enable === "true")
                .sort((a, b) => +b.insertDate - +a.insertDate);
        } catch (err) {
            const isCtiamError = CtiamCode.includes((err as any)?.data?.code);
            if (isCtiamError) {
                // 权限报错时清空数据
                this.dataList = [];
            }
            if ((err as any).raw === "cancel by user") {
                return;
            }
            this.$errorHandler(err);
        } finally {
            this.loading = false;
        }
    }

    get batchUpdate() {
        return StatisticsModule.batchUpdate;
    }

    // 过滤后数据，先根据条件筛选，再做前端分页
    get filterList() {
        // 前端过滤，0为全部
        let filterList = this.dataList.slice();

        if (this.product !== "0") {
            filterList = filterList.filter(item => item.productCode + "" === this.product);
        }
        if (this.status !== "0") {
            filterList = filterList.filter(item => item.status + "" === this.status);
        }
        const searchVal = this.searchVal.trim();
        if (searchVal) {
            filterList = filterList.filter(item => item.domain.includes(searchVal));
        }
        const { selectedLableValueList, domainMapLabel } = this;
        filterList = filterList.filter(item => {
            // 没有已选标签就不过滤了
            if (selectedLableValueList.length === 0) {
                return true;
            } else {
                return selectedLableValueList.some(labelId => domainMapLabel[item.domain]?.includes(labelId));
            }
        });
        filterList = filterList
            .filter(item => item.status !== 8)
            .map((item, index) => ({
                ...item,
                sortIdx: index, // 用于解决排序不稳定问题，在排序前预存当前顺序，而不是使用排序过程中会变的 index
            }))
            .sort((a, b) => {
                const isConfiguring = (a: DomainItem) => a.status === 3;
                // 有在途工单的往前排，如果二者相同，则使用 sortIdx 保持原顺序不变
                return +isConfiguring(b) - +isConfiguring(a) || a.sortIdx - b.sortIdx;
            })
            .map((item, index) => ({
                ...item,
                index: index + 1, // 增加序号
            }));

        return filterList;
    }

    // 最终展示的分页数据
    get showList() {
        // 过滤完成后，再分页
        const start = (this.page - 1) * this.pageSize;
        const end = this.page * this.pageSize;
        const showList = this.filterList.slice(start, end);
        return showList;
    }

    @Watch("status")
    private onStatusChange() {
        this.page = 1;
    }
    @Watch("searchVal")
    private onSearchValChange() {
        this.page = 1;
    }

    private refresh(type: string) {
        if (type === "reset") {
            const domainTableRef = this.$refs.domainTable as ElTable;
            domainTableRef.clearSelection();
            this.product = "0";
            this.status = "0";
            this.searchVal = "";
            this.page = 1;
            this.pageSize = 10;
            // 重置标签
            this.selectedLableValueList = [];
            this.timer = new Date().getTime();
        }
        this.search();
    }

    private async download() {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListExport"));

        let str = `${this.$t("domain.list.downloadStr")}\n`;
        for (const item of this.filterList) {
            const origins = item.origins || [];

            if (origins.length > 0) {
                // 如果有源站信息，为每个源站创建一行
                origins.forEach((origin, originIndex) => {
                    if (originIndex === 0) {
                        // 第一行包含完整的域名信息
                        str += item.index + ",";
                        str += item.domain + ",";
                        str += (item.cname || "") + ","; // 接口中的个别数据没有传递cname，避免产生错位
                        str += this.productFormatter(item) + ",";
                        str +=
                            this.$t(`${DomainStatusMap[item.status as keyof typeof DomainStatusMap]}`) + ",";
                        str += timeFormat(item.insertDate) + ",";
                    } else {
                        // 其余行的域名信息留空
                        str += ",";
                        str += ",";
                        str += ",";
                        str += ",";
                        str += ",";
                        str += ",";
                    }

                    // 源站地址
                    str += `"${origin.origin}",`;

                    // 源站角色
                    const roleText = origin.role === "master" ? "主源" : "备源";
                    str += `"${roleText}",`;

                    // 源站权重
                    str += `"${origin.weight}",`;

                    // 回源HOST
                    str += `"${origin.origin_host || ""}",`;

                    if (originIndex === 0) {
                        // 只在第一行添加域名级别的字段
                        // http回源端口
                        str += `"${item?.http_origin_port || ""}",`;

                        // https回源端口
                        str += `"${item?.https_origin_port || ""}",`;

                        // 回源host
                        str += `"${item.req_host || ""}",`;

                        // 是否跟随请求端口回源
                        const followRequestBackport = item.follow_request_backport === 1 ? "是" : "否";
                        str += `"${followRequestBackport}"`;
                    } else {
                        // 其余行的域名级别字段留空
                        str += ",";
                        str += ",";
                        str += ",";
                        str += "";
                    }

                    str += "\n";
                });
            } else {
                // 如果没有源站信息，创建一行空的源站数据
                str += item.index + ",";
                str += item.domain + ",";
                str += (item.cname || "") + ","; // 接口中的个别数据没有传递cname，避免产生错位
                str += this.productFormatter(item) + ",";
                str += this.$t(`${DomainStatusMap[item.status as keyof typeof DomainStatusMap]}`) + ",";
                str += timeFormat(item.insertDate) + ",";
                // 源站地址,源站角色,源站权重,回源HOST
                str += `"","","","",`;
                // http回源端口,https回源端口,回源host,是否跟随请求端口回源
                str += `"${item?.http_origin_port || ""}",`;
                str += `"${item?.https_origin_port || ""}",`;
                str += `"${item.req_host || ""}",`;
                const followRequestBackport =
                    item.follow_request_backport === 1
                        ? this.$t("domain.editPage.label24")
                        : this.$t("domain.editPage.label25");
                str += `"${followRequestBackport}"`;
                str += "\n";
            }
        }
        downloadCsv(`${this.$t("domain.list.downloadName")}`, str);
    }

    private sizeChange(val: number) {
        // 恢复到第一页
        this.page = 1;
        this.pageSize = val;
    }

    private timeFormatter(row: DomainItem) {
        return timeFormat(row.insertDate);
    }

    // 通用：警告提示
    private showAlertMsg(msg: string, title = "", type: MessageType = "warning") {
        this.$alert(msg, title || (this.$t("common.messageBox.title") as string), {
            confirmButtonText: this.$t("common.dialog.submit") as string,
            type,
        });
    }

    acceAreaDialogCloseWithRefresh() {
        this.currentUpdateAreaDomain.visible = false;
        this.search();
    }

    // 跳转编辑前，判断是否为叠加域名，按需给与提示
    private async beforeGoEdit(row: DomainItem) {
        const canDo = await this.checkDomainIsRepeat(row.domain);
        if (canDo) {
            this.$router.push({
                name: "ndomain.edit",
                query: {
                    domain: row.domain,
                    status: `${row.status}`,
                    opType: "edit",
                    creating: row.creating,
                },
            });
        }
    }

    // 判断域名是为叠加域名
    async checkDomainIsRepeat(domain: string) {
        this.loading = true;
        const { isRepeat } = await this.$ctFetch<{ isRepeat: string }>(domainValidateUrl, {
            method: "POST",

            data: {
                domain: domain,
                operationType: "update",
            },
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.loading = false;

        if (isRepeat) {
            await this.$confirm(`${this.$t("domain.list.tip9")}`, `${this.$t("domain.list.note")}`, {
                confirmButtonText: `${this.$t("domain.comfirm")}`,
                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                type: "warning",
            });
            return true;
        } else {
            return true;
        }
    }

    async onOperation(row: DomainItem, type: string) {
        const rowItem: any = row;
        if (type === "view") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("domainDetail"), row.domain);
            this.$router.push({
                name: "ndomain.detail",
                query: {
                    domain: row.domain,
                    status: `${row.status}`,
                    opType: "view",
                    creating: row.creating,
                },
            });
        } else if (type === "edit") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("domainSingleModify"), row.domain);
            this.beforeGoEdit(row);
            DomainModule.nGetDomainAllEditIcon();
        }
    }

    async ipv6_enable_change(row: DomainItem, active_val: number) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListChangeAcceType"), row.domain);

        const canDo = await this.checkDomainIsRepeat(row.domain);
        if (!canDo) {
            return;
        }
        let message = this.$i18n.t("domain.ipv6.tip1");
        if (active_val === 1) {
            message = this.$i18n.t("domain.ipv6.tip2");
        }
        await this.$confirm(message as string, this.$t("domain.list.note") as string, {
            confirmButtonText: this.$i18n.t("common.dialog.submit") as string,
            cancelButtonText: this.$i18n.t("common.dialog.cancel") as string,
            showClose: false,
            closeOnPressEscape: false,
            closeOnClickModal: false,
            type: "warning",
            beforeClose: async (action, instance, done) => {
                if (action !== "confirm") {
                    done();
                    return;
                }

                try {
                    const params = {
                        domain: row.domain,
                        enableIpv6: active_val === 1,
                    };
                    instance.confirmButtonLoading = true;
                    instance.cancelButtonLoading = true;
                    await this.$ctFetch(nDomainUrl.ipv6EnableUrl, {
                        method: "POST",
                        data: params,
                        headers: {
                            "Content-Type": "application/json",
                        },
                    });
                    this.$message.success(this.$i18n.t("domain.ipv6.tip3") as string);
                    setTimeout(() => {
                        this.search();
                    }, 100);
                } catch (e) {
                    this.$errorHandler(e);
                    done();
                    return;
                } finally {
                    instance.confirmButtonLoading = false;
                    instance.cancelButtonLoading = false;
                }

                done();
                return;
            },
        });
    }

    async premium_network_change(row: any, active_val: number) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListChangeAcceType"), row.domain);

        this.rowData = row;
        this.premium_network_switch = active_val;
        if (active_val === 1) {
            this.sceneDialogVisible = true;
        } else {
            this.virtual_scene = row.virtual_config && row.virtual_config.virtual_scene;
            const message = this.$i18n.t("domain.list.tableLabelPremiumNetworkTip4");
            await this.$confirm(message as string, this.$t("domain.list.note") as string, {
                confirmButtonText: this.$i18n.t("common.dialog.submit") as string,
                cancelButtonText: this.$i18n.t("common.dialog.cancel") as string,
                showClose: false,
                closeOnPressEscape: false,
                closeOnClickModal: false,
                type: "warning",
                beforeClose: async (action, instance, done) => {
                    if (action !== "confirm") {
                        done();
                        return;
                    }

                    try {
                        instance.confirmButtonLoading = true;
                        instance.cancelButtonLoading = true;
                        await this.commitPremiumNetworkScene(row);
                    } catch (e) {
                        this.$errorHandler(e);
                        done();
                        return;
                    } finally {
                        instance.confirmButtonLoading = false;
                        instance.cancelButtonLoading = false;
                    }

                    done();
                    return;
                },
            });
        }
    }

    private premiumNetworkSceneCancel() {
        this.sceneDialogVisible = false;
    }

    async premiumNetworkSceneSubmit() {
        try {
            this.updateLoading = true;
            await this.commitPremiumNetworkScene(this.rowData);
            this.sceneDialogVisible = false;
            this.updateLoading = false;
        } catch (e) {
            this.updateLoading = false;
            this.$errorHandler(e);
        } finally {
            this.updateLoading = false;
        }
    }
    async commitPremiumNetworkScene(row: any) {
        const params = {
            domain: row.domain,
            virtual_config: {
                virtual_status: this.premium_network_switch,
                virtual_scene: this.virtual_scene,
            },
        };
        await this.$ctFetch(nDomainUrl.virtualEnableUrl, {
            method: "POST",
            data: params,
            headers: {
                "Content-Type": "application/json",
            },
        });
        this.$message.success(this.$i18n.t("domain.list.tableLabelPremiumNetworkTip6") as string);
        setTimeout(() => {
            this.search();
        }, 100);
    }

    private virtualSceneChange(val: any) {
        this.virtual_scene = val;
    }

    get cdnAcceleratesLink() {
        return this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/products/zh-cn/10015926"
                : "https://www.esurfingcloud.com/products/10015926"
            : "https://www.ctyun.cn/products/cdnjs";
    }

    async changeAccelerationType(row: DomainItem, jumpRepeat = false): Promise<boolean> {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListChangeAcceType"), row.domain);

        if (!jumpRepeat) {
            const canDo = await this.checkDomainIsRepeat(row.domain);
            if (!canDo) {
                return false;
            }
        }

        if (!(this.canUseList || []).find(item => item.product_code === "008")) {
            await this.$prompt(
                this.$t("domain.detail.tip8", { link: this.cdnAcceleratesLink }) as string,
                this.$t("common.messageBox.title") as string,
                {
                    showInput: false,
                    showCancelButton: false,
                    dangerouslyUseHTMLString: true,
                }
            );
            return false;
        }
        let updateSucc = false;
        // 对新旧产品的进制与系数是否一致
        let isSame = false;
        try {
            this.acceTypeChangeTableData = await this.$ctFetch(getRatioAndScale, {
                data: { productType: row.productCode + ",008" },
            });
        } catch (e) {
            this.$errorHandler(e);
            return false;
        }
        const pre = this.acceTypeChangeTableData[0];
        const cur = this.acceTypeChangeTableData[1];
        if (pre && cur) {
            isSame =
                (pre.scale || 1000) === (cur.scale || 1000) &&
                (parseInt(pre.ratio) || 10) === (parseInt(cur.ratio) || 10);
        } else {
            return false;
        }
        // const isSame = false;
        if (isSame) {
            await this.$confirm(
                this.$t("simpleForm.acceArea.confirmChangeAccelerationType") as string,
                this.$t("domain.create.tip3-1") as string,
                {
                    showClose: false,
                    closeOnPressEscape: false,
                    closeOnClickModal: false,
                    type: "warning",
                    confirmButtonText: this.$t("common.dialog.submit") as string,
                    cancelButtonText: this.$t("common.dialog.cancel") as string,
                    beforeClose: async (action, instance, done) => {
                        if (action !== "confirm") {
                            done();
                            return;
                        }

                        instance.confirmButtonLoading = true;
                        instance.cancelButtonLoading = true;
                        updateSucc = await this.submitTicket(row, () => {
                            done();
                            instance.confirmButtonLoading = false;
                            instance.cancelButtonLoading = false;
                        });
                    },
                }
                // eslint-disable-next-line
            ).then(() => {}).catch(() => {});
        } else {
            this.acceTypeChangeCallback = async () => {
                updateSucc = await this.submitTicket(row);
                return updateSucc;
            };
            this.showAcceTypeChangeDailog = true;
        }

        return updateSucc;
    }

    private domainCountLimitSubmit() {
        this.domainCountLimitDialogVisible = false;
    }

    /**
     * 提交变更类型工单
     * @param row 域名数据
     * @param callback finally回调
     * @returns 是否提交成功
     *  */
    private async submitTicket(row: any, callback?: Function): Promise<boolean> {
        let status = false;
        try {
            await this.$ctFetch(nDomainUrl.updateAreaOrProduct, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                data: { type: "product", domain: row.domain, newBusinessType: 8 },
            });
            this.$message.success(this.$t("domain.detail.tip7") as string);
            setTimeout(() => {
                this.search();
            }, 100);
            status = true;
        } catch (e) {
            // 如果code等于 clnt.e2396, 需要弹窗展示：CDN加速域名个数配额不足，详见：使用限制。
            const code = (e as any)?.data?.code;
            if (code === CODE_QUOTA_EXCEEDED_2396) {
                this.domainCountLimitLink = (e as any)?.data?.reason;
                this.domainCountLimitDialogVisible = true;
            } else {
                this.$errorHandler(e);
            }
        } finally {
            callback && callback();
        }
        return status;
    }

    get acceAreaTipLearnMore() {
        const lang = nUserModule.lang === "zh" ? "/zh-cn" : "";
        return this.isCtclouds
            ? `https://www.esurfingcloud.com/document${lang}/10015932/20688150`
            : "https://www.ctyun.cn/document/10015932/10129365";
    }

    async changeAccelerationArea(row: DomainItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListChangeAcceArea"), row.domain);

        // 如果域名已开启高性能网络配置，即：高性能网络开关为开，则不允许变更加速区域。
        const virtual_status =
            (row as any).virtual_config && (row as any).virtual_config.virtual_status === 1;
        if (virtual_status) {
            this.accelerationAreaDialogTip(row);
            return;
        }
        const canDo = await this.checkDomainIsRepeat(row.domain);
        if (!canDo) {
            return;
        }

        this.currentUpdateAreaDomain = {
            row,
            visible: true,
        };
    }

    async accelerationAreaDialogTip(row: DomainItem) {
        const message = this.$i18n.t("domain.list.tableLabelPremiumNetworkTip7");
        await this.$confirm(message as string, this.$t("domain.list.note") as string, {
            confirmButtonText: this.$i18n.t("common.dialog.submit") as string,
            cancelButtonText: this.$i18n.t("common.dialog.cancel") as string,
            showClose: false,
            closeOnPressEscape: false,
            closeOnClickModal: false,
            showCancelButton: false,
            type: "warning",
            beforeClose: async (action, instance, done) => {
                done();
                return;
            },
        });
    }

    // 操作域名     启停删操作不用判断是否为重叠域名
    private async beforeUpdateDomain(row: DomainItem, status: number) {
        const actionMap: Record<number, keyof typeof CtiamButtonEnum> = {
            1: "domainListDelete",
            2: "domainListDisable",
            3: "domainListEnable",
        };
        await checkCtiamButtonAuth(GetCtiamButtonAction(actionMap[status]), row.domain);

        this.changeDomainStatus(row, status);
    }

    private openWorksheet() {
        const link = this.isCtclouds
            ? nUserModule.lang === "zh"
                ? "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=zh-cn"
                : "https://www.esurfingcloud.com/contactus/zh-cn/fromIndex?lang=en-us"
            : "https://www.ctyun.cn/h5/wsc/worksheet/submit";
        window.open(link);
        // window.open("https://www.ctyun.cn/h5/wsc/worksheet/submit");
    }

    private async changeDomainStatus(row: DomainItem, status: number) {
        if (status === 2) {
            // this.disableFormVisible = true;
            // this.disableForm.domain = row.domain;
            // this.disableForm.reason = "";
            // this.rowItem = row;
            const title = this.DomainAction[status];
            await this.$confirm(`${this.$t("domain.list.tip21")}`, `${this.$t("domain.list.label2")}`, {
                confirmButtonText: `${this.$t("domain.comfirm")}${title}`,
                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                type: "warning",
            });
            this.handleRequest(row, status);
        } else {
            const title = this.DomainAction[status];
            if (status === 1) {
                const escapeRegExp = (string: string) => {
                    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"); //$&表示整个被匹配的字符串
                };
                await this.$prompt(
                    `${this.$t("domain.list.tip11", { domain: row.domain })}`,
                    `${this.$t("domain.comfirm2")}`,
                    {
                        confirmButtonText: `${this.$t("domain.comfirm")} ${title}`,
                        cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                        inputPattern: new RegExp(`^${escapeRegExp(row.domain)}$`),
                        inputErrorMessage: `${this.$t("domain.list.tip12")}`,
                    }
                );
            } else {
                if (status === 3) {
                    // 反诈域名不允许启用
                    if (row.antiFraudFlag === 1) {
                        this.showAlertMsg(`${this.$t("domain.list.tip13")}`);
                        return false;
                    }

                    // 产品列表中是 _ 命名，域名列表中是驼峰命名
                    const product = this.ProductList.find(
                        product => product.product_code === row.productCode
                    );

                    // 当加速类型为流量包 flow_packet，且流量包处于2：使用中以外的状态，则无法启用
                    // 前端屏蔽启用域名校验服务中产品与产品类型一致性逻辑，由后端校验即可
                    // if (!product || (product.product_type === "flow_packet" && product.status !== 2)) {
                    //     this.showAlertMsg(`${this.$t("domain.list.tip14")}`);
                    //     return false;
                    // }

                    // 当加速类型为按需付费 bss_product，且处于 4: '已冻结（暂停）', 5: '已删除', 7: '冻结中', 9: '删除中（销户中）' 任一状态中时，不能启用
                    // const productDone = [4, 5, 7, 9];
                    // if (
                    //     product &&
                    //     product.product_type === "bss_product" &&
                    //     productDone.indexOf(product.status) > -1
                    // ) {
                    //     this.showAlertMsg(
                    //         `${this.$t("domain.list.tip15", { status: ProductStatusMap[product.status] })}`
                    //     );
                    //     return false;
                    // }

                    this.loading = true;
                    // 注意，此接口返回的两个字段：isExist和recordStatus均为String类型
                    const result: any = await this.$ctFetch(domainValidateUrl, {
                        headers: {
                            "Content-Type": "application/json",
                        },
                        method: "POST",
                        data: {
                            domain: row.domain,
                            operationType: "start",
                        },
                    });
                    this.loading = false;

                    // 备案校验
                    // 根据接口获得域名备案状态
                    const recordStatus = result.recordStatus; // 1：未备案，2：已备案，3：接口异常
                    if (recordStatus === 3) {
                        // 校验接口异常：SCC接口异常，SOC接口失效
                        await this.$confirm(
                            `${this.$t("domain.list.tip16")}`,
                            `${this.$t("domain.list.tip16-1")}`,
                            {
                                confirmButtonText: `${this.$t("domain.comfirm3")}`,
                                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                            }
                        );
                    } else if (recordStatus === 1) {
                        // 校验不通过

                        // 域名加速区域为全球（不含中国内地），预期不需要备案校验 --areaScope
                        if (row.areaScope !== 2) {
                            const h = this.$createElement;
                            await this.$msgbox({
                                title: this.$t("domain.info") as string,
                                message: h("p", undefined, [
                                    h("span", undefined, `${this.$t("domain.list.tip17")}`),
                                    h(
                                        "a",
                                        { class: "record-a", on: { click: this.openWorksheet } },
                                        `${this.$t("domain.list.tip17-1")}`
                                    ),
                                ]),
                                showCancelButton: true,
                                confirmButtonText: `${this.$t("domain.comfirm")}`,
                                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                            });
                            return;
                        }
                    } else {
                        await this.$confirm(
                            `${this.$t("domain.list.tip18", { title: title })}`,
                            `${title}${this.$t("domain.comfirm")}`,
                            {
                                confirmButtonText: `${this.$t("domain.comfirm")}${title}`,
                                cancelButtonText: `${this.$t("common.dialog.cancel")}`,
                                type: "warning",
                            }
                        );
                    }
                }
            }
            this.handleRequest(row, status);
        }
    }

    // private async handleDisable() {
    //     this.disableFormVisible = false;
    //     let reason = "";
    //     reason = this.disableForm.reason.replace(/\s*/g, "");
    //     const status = 2;
    //     this.handleRequest(this.rowItem, status, reason);
    // }

    private async handleRequest(row: DomainItem, status: number, reason = "") {
        const title = this.DomainAction[status];
        this.loading = true;
        // 考虑工单从队列中捞取的时延问题，需要延迟确认操作结果后再刷新列表
        await delayedResponse(
            this.$ctFetch(nDomainUrl.changeDomainStatus, {
                //encodeParams: true,
                method: "POST",
                data: {
                    domain: row.domain, // 域名
                    userName: this.UserInfo.name, // 需要前端传递，作为兜底
                    status, // 域名目标状态 1. 删除 2.停用 3.启用
                    domainStatus: row.status, // 域名当前状态
                    businessType: row.productCode, // 为了 reason 字段新需求，需要加多这个字段
                    recordNum: row.recordNum, // 增加备案号字段
                    createId: row.createId || "", // 部分数据中该字段可能不存在
                    signature: row.signature,
                    reason: String(reason),
                },
                headers: {
                    "Content-Type": "application/json",
                },
            }),
            2000
        );

        // 本地锁存编辑状态
        this.setDomainChangeCache({
            d: row.domain,
            D: +new Date(),
        });

        this.$message(
            `${this.$t("domain.list.tip19", {
                title: (() => {
                    if (!title) return "";
                    if (nUserModule.lang === "en") return title[0].toLocaleLowerCase() + title.slice(1);
                    return title;
                })(),
            })}`
        );
        // 由于需要获取工单id用于跳转详情，所以需要刷新列表，但是分页等搜索条件不变
        setTimeout(() => {
            // 延后更新，避免 loading 状态被提前取消，用户感知不到刷新
            this.search();
        }, 100);
    }

    private productFormatter(row: DomainItem) {
        //如果为二级产品则返回二级产品名称
        return getI18nLabel(row.subProductCode || row.productCode);
    }

    // 加速区域列
    private areaFormatter(row: DomainItem) {
        if (row.areaScope === 1) {
            return this.$t("domain.areaScope[0]");
        } else if (row.areaScope === 2) {
            return this.$t("domain.areaScope[1]");
        } else {
            return this.$t("domain.areaScope[2]");
        }
    }

    private virtualSdceneFormatter(row: any) {
        const virtual_scene = (row.virtual_config && row.virtual_config.virtual_scene) || null;
        if (virtual_scene === 1) {
            return this.$t("domain.list.scene[0]");
        } else if (virtual_scene === 2) {
            return this.$t("domain.list.scene[1]");
        } else if (virtual_scene === 3) {
            return this.$t("domain.list.scene[2]");
        } else {
            return "";
        }
    }

    async judgeBeforeNew(type: string) {
        if (type === "batch") {
            await checkCtiamButtonAuth(GetCtiamButtonAction("domainBatchCreate"));
        } else {
            await checkCtiamButtonAuth(GetCtiamButtonAction("domainSingleCreate"));
        }

        // 无产品时无法创建域名
        if (this.canUseList.length === 0) {
            await this.$prompt(`${this.$t("domain.list.tip20")}`, `${this.$t("common.messageBox.title")}`, {
                showInput: false,
                showCancelButton: false,
            });
        } else {
            if (type === "batch") {
                this.$router.push({
                    name: "ndomain.create",
                    query: {
                        type: type,
                    },
                });
                return;
            }

            this.$ctUtil.redirect("#/ndomain/create", true);
        }
    }

    private async addLabel(row: DomainItem) {
        await checkCtiamButtonAuth(GetCtiamButtonAction("domainListLabel"), row.domain);

        this.domainItem = row;
        this.labelVisible = true;
    }

    private cancel() {
        this.labelVisible = false;
    }

    // 判断是否有批量新增域名功能
    async checkBatcCreate() {
        this.$ctFetch<{ batchCreate: boolean }>(nBasicConfig, { cache: true })
            .then(rst => {
                this.batchCreate = rst.batchCreate;
            })
            .catch(err => this.$errorHandler(err))
            .finally(() => {
                // async babel 插件中会默认为 async await 添加 finally 语句块，导致初始化时 loading 状态提前结束
                // 所以此处需要添加一个空函数，避免loading状态提前结束
                if (this.loading) return;
            });
    }

    private canChangeAccelerationType(row: DomainItem) {
        // 域名不是已启用状态，不允许修改加速区域
        if (row.status !== 4) {
            return false;
        }
        // 加速类型为视频加速、静态加速、下载加速，才展示加速区域变更图标按钮。
        if (!["001", "003", "004"].includes(row.productCode)) {
            return false;
        }

        return true;
    }

    private canChangeAccelerationArea(row: DomainItem) {
        // 域名不是已启用状态，不允许修改加速区域
        if (row.status !== 4) {
            return false;
        }

        // 全站加速 子产品
        if (row.productCode === "006" && row.subProductCode) {
            return false;
        }

        // 加速类型为 视频加速、静态加速、下载加速、全站加速（不包括子产品）、CDN加速，才展示加速区域变更图标按钮。
        if (!["001", "003", "004", "006", "008"].includes(row.productCode)) {
            return false;
        }

        return true;
    }

    private canChangeIpv6(row: DomainItem) {
        // 视频直播不支持修改配置、非启用状态的域名不支持修改、产品在使用期限内
        return row.status !== 4 || row.productCode === "005";
    }

    private canChangePremiumNetwork(row: DomainItem) {
        // 视频直播不支持修改配置、非启用状态的域名不支持修改、产品在使用期限内、或域名的加速范围为中国内地，则开关直接置灰，不允许点击
        return row.areaScope === 1 || row.status !== 4 || row.productCode === "005";
    }

    private mounted() {
        this.$ctBus.$on("refershDomainList", () => {
            this.search();
        });
    }
    private beforeDestroy() {
        this.$ctBus.$off("refershDomainList");
    }
}
</script>

<style lang="scss" scoped>
// 状态颜色
// .status-1,
// .status-3 {
//     color: $g-color-blue;
// }
// .status-2,
// .status-4 {
//     color: $g-color-green;
// }
// .status-5,
// .status-7,
// .status-9,
// .status-10,
// .status-11,
// .status-12 {
//     color: $g-color-red;
// }
// .status-6,
// .status-8 {
//     color: $g-color-gray;
// }
.search-panel-wrap {
    display: flex;
    justify-content: space-between;

    .search-bar {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
    }

    .search-btns {
        display: flex;
        gap: 4px;
        height: fit-content;
    }
}
.ist-pager {
    // @include g-mg-tp(4px, 8px, 12px);

    ::v-deep .el-pagination {
        text-align: right;
    }
}

.el-link {
    font-size: 12px;
    vertical-align: 0px;
}

// 按钮间的竖分割线
// .el-table {
// .ct-select,
// ::v-deep .cell > .el-button:not(:first-child) {
//     position: relative;
//     margin-left: 8px;
// &::before {
//     content: "";
//     position: absolute;
//     height: 16px;
//     border-left: 1px solid $border-color;
//     top: 50%;
//     left: -6px;
//     transform: translateY(-50%);
// }
// }
// }

// 按钮组内的按钮
.ct-select__dropdown .el-button {
    display: block;
    margin: 0 10px;
}

// ... 更多按钮
.more-operation {
    ::v-deep .el-icon-more {
        transform: rotate(90deg);
    }
}

// 标签图标
.label-icon {
    font-size: 16px;
}
// 新增标签
.label-add {
    color: $color-master;
    &:hover {
        cursor: pointer;
    }
}

.record-a {
    color: $color-master;
    text-decoration: underline;
    &:hover {
        cursor: pointer;
        color: $color-master;
    }
}

.list-cell-wrapper {
    display: flex;
    gap: 4px;
    flex-wrap: no-wrap;
    &-icon {
        font-size: 16px;
        cursor: pointer;
        align-self: center;
    }
}
.scene-text {
    margin-left: 12px;
}
.table-button-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
::v-deep {
    .el-divider {
        margin-top: 12px !important;
    }
}
</style>
