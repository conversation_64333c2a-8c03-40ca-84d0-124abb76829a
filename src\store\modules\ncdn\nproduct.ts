import { VuexModule, Module, Action, Mutation, getModule } from "vuex-module-decorators";

import store from "@/store/index";
import { ctFetch } from "@/utils";
import { nProductList } from "@/config/url";

import { SelectOptions } from "@/types/common";
import { DomainItem } from "@/types/domain";
import { ProductInfo, ProductState } from "@/store/types";
import { ProductType, ProductCanUseStatus, ShowProductMap, ProductItemValueMap } from "@/store/config";
import i18n from "@/i18n";
import router from "@/router";

/**
 * 获取两个数组的并集
 *
 * 此函数旨在合并两个SelectOptions类型的数组，通过指定的键值（"value"或"label"）来去除重复项
 * 它首先将两个数组连接在一起，然后通过reduce方法遍历，使用一个对象来跟踪已遍历的键值，
 * 从而确保结果数组中只包含唯一的项这个过程依赖于obj[cur[index]]的值，
 * 如果该值不存在或者为false，表示当前项是新出现的，应当被包含在结果数组中
 *
 * @param index - 指定用作唯一标识的键名，可以是"value"或"label"
 * @param arr1 - 第一个数组，默认为空数组
 * @param arr2 - 第二个数组，默认为空数组
 * @returns 合并去重后的SelectOptions数组
 */
function getArrayUnion(index: "value" | "label", arr1: SelectOptions[] = [], arr2: SelectOptions[] = []) {
    // 将两个数组连接成一个新的数组
    const arr3 = arr1.concat(arr2);
    // 创建一个对象用于存储已遍历的键值，以避免重复项
    const obj: { [productCode: string]: boolean } = {};
    // 使用reduce方法遍历数组，过滤出不重复的项
    return arr3.reduce((prev, cur) => {
        // 如果当前项的键值不存在于obj中，则将其添加到结果数组中
        if (!obj[cur[index]]) {
            obj[cur[index]] = true;
            prev.push(cur);
        }
        // 返回累积的数组，即去重后的结果
        return prev;
    }, [] as SelectOptions[]);
}

/**
 * 根据提供的itemValue搜索并返回对应的label。
 * 此函数通过遍历ShowProductMap的键，寻找包含itemValue的键，并返回该键对应的值。
 * 如果没有找到匹配的键，则返回默认的项目名称标识"common.productName[11]"。
 *
 * @param itemValue - 用于搜索的项目值，可能是产品ID或其他标识符。
 * @returns 对应的label或默认的项目名称标识。
 */
function getItemLabel(itemValue: string): string {
    // 遍历ShowProductMap的所有键
    for (const key of ShowProductMap.keys()) {
        // 检查当前键是否包含itemValue
        if (key.includes(itemValue)) {
            // 如果找到，返回该键对应的值
            return ShowProductMap.get(key) as string;
        }
    }
    // 如果没有找到匹配的键，返回默认的项目名称标识
    return "common.productName[11]";
}

/**
 * 根据给定的项目值(itemValue)查找并返回相应的字符串值。
 *
 * 该函数通过搜索一个预定义的映射(ProductItemValueMap)来找到对应的值。
 * 如果找到匹配的键，则返回相应的值，否则返回"000"。
 *
 * @param itemValue - 用于在映射中进行查找的项目值字符串。
 * @returns 对应的字符串值，如果找不到则返回"000"。
 */
function getItemValue(itemValue: string): string {
    // 遍历映射的键
    for (const key of ProductItemValueMap.keys()) {
        // 检查键是否包含项目值
        if (key.includes(itemValue)) {
            // 如果找到，返回对应的值
            return ProductItemValueMap.get(key) as string;
        }
    }
    // 如果没有找到匹配的键，返回默认值"000"
    return "000";
}

/**
 * 将一组字符串转换为对应的代码数组
 * 此函数通过查询ProductItemValueMap映射表，将输入的字符串数组(valueArr)中的每个值转换成相应的代码字符串数组
 *
 * @param valueArr 输入的字符串数组，每个元素将被转换成对应的代码
 * @returns 返回一个字符串数组，包含输入数组中每个元素对应的代码
 */
function getCodeArr(valueArr: string[]): string[] {
    // 初始化一个空数组，用于存储转换后的代码字符串
    const codeArr: string[] = [];

    // 遍历输入的字符串数组
    for (const itemValue of valueArr) {
        // 遍历ProductItemValueMap映射表的所有键
        for (const key of ProductItemValueMap.keys()) {
            // 如果当前键包含当前项的值
            if (key.includes(itemValue)) {
                // 将键对应的值（代码字符串）添加到codeArr数组中
                codeArr.push(ProductItemValueMap.get(key) as string);
                // 完成当前值的转换后，跳出内层循环
                break;
            }
        }
    }
    // 返回转换后的代码数组
    return codeArr;
}

// 根据源数据list，得到下拉框要展示的加速类型
function getShowProduct(list: ProductInfo[]) {
    let productArr: SelectOptions[] = [];
    let codeArr = [];
    // 保存list中所有的产品编码
    codeArr = list.map(item => {
        return item.product_code;
    });
    // 根据产品编码得到展示数据中对应的编码
    // 例如，"008"和"120"均得出"008"
    codeArr = getCodeArr(codeArr);
    // 去重
    codeArr = [...new Set(getCodeArr(codeArr))];
    productArr = codeArr.map(itemCode => {
        return {
            label: getItemLabel(itemCode), // 根据编码得到要展示的label值
            value: getItemValue(itemCode), // 根据编码得到与lable值对应的value值
        };
    });
    return productArr;
}

@Module({ dynamic: true, store, name: "product" })
class Product extends VuexModule implements ProductState {
    public list: ProductInfo[] = []; // 产品列表接口源数据
    public productOptions: SelectOptions[] = []; // 由源数据生成的选项列表
    public allProductOptions: SelectOptions[] = []; // 所有加速类型，包括域名中存在但该产品目前不存在的情况
    public canUseList: ProductInfo[] = []; // 过滤出的可用状态下的产品列表
    public canUseProductOptions: SelectOptions[] = [];
    public dedicatedLine = false;
    public showCDNPremiumNetworkSwitch = false; // CDN加速有开通高性能网络：CDN加速-高性能网络product_code=189 ,服务中status=1
    public showWholeStationPremiumNetworkSwitch = false; // 全站加速有开通高性能网络：全站加速-高性能网络product_code=190，服务中status=1

    @Mutation
    private SET_PRODUCT_LIST(list: ProductInfo[] = []) {
        // 兼容无数据情况
        this.list = list;
        this.productOptions = getShowProduct(list);
        // 过滤可用状态的数据，按需产品状态1：服务中，流量包产品状态2：使用中
        const canUseList = list.filter(
            p =>
                (p.product_type === ProductType.BSS && p.status === ProductCanUseStatus.BSS) ||
                (p.product_type === ProductType.FLOW && p.status === ProductCanUseStatus.FLOW)
        );
        this.canUseList = canUseList;
        this.canUseProductOptions = canUseList.map(item => ({
            label: item.product_cname,
            value: item.product_code,
        }));
        // CDN加速有开通高性能网络
        this.showCDNPremiumNetworkSwitch = list.some(
            item => (item.product_code as any) === "189" && item.status === ProductCanUseStatus.BSS
        );
        // 全站加速有开通高性能网络
        this.showWholeStationPremiumNetworkSwitch = list.some(
            item => (item.product_code as any) === "190" && item.status === ProductCanUseStatus.BSS
        );
    }

    @Mutation
    private SET_ALL_PRODUCT_LIST(list: SelectOptions[] = []) {
        this.allProductOptions = list;
    }
    @Mutation
    SET_DEDICATED_LINE(data: boolean) {
        this.dedicatedLine = data;
    }

    @Action
    public async nGetProductInfo() {
        const { lang } = this.context.rootState.user;
        const {
            list = [],
            dedicatedLine = false,
            hasProduct = false,
            packageCount = 0,
        }: {
            list: ProductInfo[];
            dedicatedLine: any;
            hasProduct: boolean;
            packageCount: number;
        } = await ctFetch(nProductList, {
            data: {
                language: lang,
            },
            cache: true,
        });

        if (router.currentRoute.name !== "forbidden" && !hasProduct && packageCount === 0) {
            router.push({ name: "forbidden", query: { type: "service" } });
        }

        this.SET_PRODUCT_LIST(list);
        this.SET_DEDICATED_LINE(dedicatedLine);
    }

    // 后端的产品列表接口，可能存在退订、过期导致产品列表的加速类型不全
    // 获取所有域名的加速类型种类
    @Action
    public GetAllProduct(list: DomainItem[]) {
        const buffer: { [productCode: string]: boolean } = {};
        const domainProduct: { label: string; value: string }[] = [];
        // 获取每个域名的加速类型，并去重
        for (let i = 0; i < list.length; i++) {
            if (!buffer[list[i].productCode]) {
                domainProduct.push({
                    label: getItemLabel(list[i].productCode),
                    value: list[i].productCode,
                });
                buffer[list[i].productCode] = true;
            }
        }
        // 取两个数组对象并集
        this.SET_ALL_PRODUCT_LIST(getArrayUnion("value", this.productOptions, domainProduct));
    }
}

export const ProductModule = getModule(Product);

/**
 * Retrieves the internationalization label for a given product code.
 *
 * @param {string} productCode - The product code to retrieve the label for.
 * @return {string} The internationalization label for the given product code.
 */
export function getI18nLabel(productCode: string): string {
    return i18n.t(getItemLabel(productCode)) as string;
}
