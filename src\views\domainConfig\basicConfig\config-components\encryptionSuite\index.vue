<template>
    <div class="ct-edit-wrapper">
        <el-form
            :label-width="labelWidth"
            :label-position="labelPosition"
            :model="form"
            :rules="rules"
            ref="encryptionSuiteForm"
            :disabled="disabled"
        >
            <div v-if="isShow">
                <!-- 加密套件 -->
                <el-form-item :label="$t('domain.create.encryptionSuite.label1')" prop="ssl_ciphers">
                    <el-select
                        v-model="form.ssl_ciphers"
                        @change="sslCiphersChange"
                        :placeholder="$t('domain.create.encryptionSuite.placeholder1')"
                        clearable
                        class="input-style"
                    >
                        <el-option
                            v-for="item in ssl_ciphers_list"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            :disabled="item.disabled"
                        ></el-option>
                    </el-select>
                </el-form-item>

                <!-- 自定义加密套件 -->
                <el-form-item
                    :label="$t('domain.create.encryptionSuite.label2')"
                    prop="custom_ssl_ciphers"
                    :rules="rules.custom_ssl_ciphers"
                    v-show="form.ssl_ciphers === 'custom'"
                    key="custom_ssl_ciphers"
                >
                    <el-select
                        v-model="form.custom_ssl_ciphers"
                        @change="handleChange"
                        clearable
                        multiple
                        class="input-style"
                    >
                        <el-option
                            v-for="item in customSsl"
                            :key="item"
                            :label="item"
                            :value="item"
                        ></el-option>
                    </el-select>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
import componentMixin from "@/views/domainConfig/componentMixin";
import { getLang, withResolver } from "@/utils";
import { StatisticsModule } from "@/store/modules/statistics";
import { intersection } from "lodash-es";

export default {
    components: {},
    mixins: [componentMixin],
    props: {
        datas: Object,
        isFcdnCreate: Boolean,
        isHttpsStatusOpen: Boolean,
        certName: String,
        isLockRequestProtocol: Boolean,
        isSm2: Boolean, // 是否配置了国密证书
        isIntlCert: Boolean, // 是否配置了国际标准证书
    },
    data() {
        return {
            form: {
                ssl_ciphers: null,
                custom_ssl_ciphers: [],
            },
            rules: {
                ssl_ciphers: [{ required: true, validator: this.validSslCiphers, trigger: "change" }],
                custom_ssl_ciphers: [
                    { required: true, validator: this.validCustomSslCiphers, trigger: "change" },
                ],
            },
        };
    },
    computed: {
        ssl_ciphers_list() {
            return [
                { label: this.$t("domain.create.encryptionSuite.option1"), value: "all" },
                {
                    label: this.$t("domain.create.encryptionSuite.option2"),
                    value: "strong",
                    disabled: this.isSm2,
                },
                { label: this.$t("domain.create.encryptionSuite.option3"), value: "custom" },
            ];
        },
        fcdnCreateShim() {
            return {
                labelWidth: getLang() === "en" ? "190px" : "140px",
                labelPosition: "right",
            };
        },
        labelPosition() {
            if (this.isFcdnCreate) return this.fcdnCreateShim.labelPosition;
            return "right";
        },
        labelWidth() {
            if (this.isFcdnCreate) return this.fcdnCreateShim.labelWidth;
            return "150px";
        },
        disabled() {
            if (this.isFcdnCreate) return false;
            return !this.isEdit || !this.isService || this.isLockRequestProtocol;
        },
        isShow() {
            return this.isHttpsStatusOpen && this.certName;
        },
        customSsl() {
            const list = [];
            if (this.isSm2) list.push(...StatisticsModule.gmCustomSsl);
            if (this.isIntlCert) list.push(...StatisticsModule.customSsl);
            return list;
        },
    },
    watch: {
        datas: {
            deep: true,
            handler(val) {
                this.init(val);
            },
            immediate: true,
        },
        isSm2: {
            handler() {
                this.handleCertChange();
            },
            immediate: true,
        },
        isIntlCert: {
            handler() {
                this.handleCertChange();
            },
            immediate: true,
        },
    },
    methods: {
        init(v) {
            this.form.ssl_ciphers = v?.ssl_ciphers;
            this.form.custom_ssl_ciphers = v?.custom_ssl_ciphers;
        },
        sslCiphersChange() {
            this.form.custom_ssl_ciphers = [];
            this.handleChange();
        },
        handleChange() {
            this.$emit("onChange", this.form);
        },
        validSslCiphers(rule, value, callback) {
            if (this.isSm2 && value === "strong") {
                return callback(new Error(this.$t("certificate.国密证书不支持强加密套件")));
            }
            callback();
        },
        async validCustomSslCiphers(rule, value, callback) {
            if (!this.isShow || this.isLockRequestProtocol) return callback();
            if (this.form.ssl_ciphers === "custom" && !(value && value.length > 0)) {
                return callback(new Error(this.$t("domain.create.encryptionSuite.tip1")));
            }

            if (this.isSm2 && this.isIntlCert && value.length) {
                if (
                    !intersection(StatisticsModule.gmCustomSsl, value).length ||
                    !intersection(StatisticsModule.customSsl, value).length
                ) {
                    return callback(
                        new Error(
                            this.$t(
                                "domain.加密套件至少包含国际加密套件中的一种且至少包含国密加密套件中的一种"
                            )
                        )
                    );
                }
            }

            callback();
        },
        validateProcedure() {
            const result = {
                valid: true,
                msg: "",
                dom: "encryptionSuite",
            };

            const { promise, resolve } = withResolver();
            this.$refs.encryptionSuiteForm.validate((valid, fields) => {
                result.valid = valid;
                result.msg =
                    Object.values(fields)?.[0]?.[0]?.message || this.$t("domain.create.encryptionSuite.tip1");
                resolve(result);
            });

            return promise;
        },
        handleCertChange() {
            this.form.custom_ssl_ciphers = (this.form.custom_ssl_ciphers || []).filter(item =>
                this.customSsl.includes(item)
            );
            this.$emit("onChange", this.form);
        },
    },
};
</script>

<style lang="scss" scoped>
.ct-edit-wrapper {
    ::v-deep {
        .el-form--label-left .el-form-item__label {
            text-align: right;
        }
    }
}
.input-style {
    width: 380px;
}
</style>
