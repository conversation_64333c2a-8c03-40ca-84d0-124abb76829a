<template>
    <ct-section-wrap headerText="新增接入" class="domain-create-wrapper">
        <el-steps class="step-wrapper" :active="activeStep" finish-status="success">
            <el-step title="加速配置"></el-step>
            <el-step title="安全配置"></el-step>
        </el-steps>

        <el-scrollbar style="margin: 0;height: calc(100% - 134px)" view-class="no-padding-wrap-scrollbar">
            <ct-box class="ct-box-domain-create-wrapper view-box" v-show="activeStep === 0">
                <el-form
                    ref="form"
                    :model="requestParam"
                    :rules="rules"
                    label-position="right"
                    label-width="130px"
                    @validate="onValidate"
                >
                    <cute-titled-block title="基本信息">
                        <template #content>
                            <el-form-item label="接入方式" prop="access_mode">
                                <el-select
                                    v-model="requestParam.access_mode"
                                    @change="access_mode_change"
                                    style="width: 400px"
                                >
                                    <el-option label="域名接入" :value="1"></el-option>
                                    <el-option label="无域名接入" :value="2"></el-option>
                                </el-select>
                                <div v-if="requestParam.access_mode === 2" class="form-item-tip">
                                    <ct-svg-icon icon-class="info-circle" class-name="icon-column-label1" />
                                    无域名接入方式的TCP请求端口、http复用端口，https复用端口，ftp控制端口、ftp数据端口不支持80、8080、443、8443端口。关于上述端口的配置，建议使用域名接入方式。
                                </div>
                            </el-form-item>
                            <el-form-item
                                label="加速域名"
                                prop="domain"
                                key="domain"
                                :error="verify_result"
                                v-if="requestParam.access_mode !== 2"
                            >
                                <el-input
                                    v-model.trim="requestParam.domain"
                                    style="width: 400px"
                                    placeholder="请输入域名"
                                    @blur="domainBlur(requestParam.domain)"
                                ></el-input>
                            </el-form-item>
                            <el-form-item
                                label="实例名称"
                                prop="inst_name"
                                key="inst_name"
                                v-if="requestParam.access_mode === 2"
                            >
                                <el-input
                                    v-model.trim="requestParam.inst_name"
                                    style="width: 400px"
                                    placeholder="请输入不超过10个字符的中/英文/数字实例名称"
                                ></el-input>
                            </el-form-item>
                            <el-form-item label="加速区域">
                                <!-- @change="handleAreaScopeChange" -->
                                <el-radio
                                    v-for="item in areaScopeMap"
                                    :key="item.id"
                                    v-model="requestParam.area_scope"
                                    :label="item.id"
                                    :disabled="item.disabled"
                                    >{{ item.name }}</el-radio
                                >
                                <div class="form-item-tip">
                                    <ct-svg-icon
                                        icon-class="info-circle"
                                        class-name="alert-icon"
                                    ></ct-svg-icon>
                                    仅需加速中国内地用户请选择“中国内地”，仅需加速全球（不含中国内地）用户请选择“全球（不含中国内地）”，同时加速中国内地和全球（不含中国内地）用户请选择“全球”
                                </div>
                            </el-form-item>
                            <el-form-item v-if="!isJinhua" label="启用IPv6">
                                <el-switch v-model="requestParam.ipv6_enable"></el-switch>
                            </el-form-item>
                        </template>
                    </cute-titled-block>
                    <cute-titled-block title="回源配置">
                        <template v-slot:content>
                            <el-form-item label="回源策略" required prop="origin_type">
                                <el-radio-group v-model="requestParam.origin_type">
                                    <el-radio-button :label="1">择优回源</el-radio-button>
                                    <el-radio-button :label="2">按权重回源</el-radio-button>
                                    <el-radio-button :label="3">保持登录</el-radio-button>
                                </el-radio-group>
                                <div class="form-item-tip">
                                    <ct-svg-icon
                                        icon-class="info-circle"
                                        class-name="alert-icon"
                                    ></ct-svg-icon>
                                    选择“择优回源”时，优先回最快的源站，忽略权重；选择“按权重回源”时，按照配置的权重回源；选择“保持登录”时则基于客户端IP哈希回源。
                                </div>
                            </el-form-item>
                            <backSourceConf
                                ref="backSourceConf"
                                :data="requestParam.forward_origins"
                                :origin-type="requestParam.origin_type"
                                :access-mode="requestParam.access_mode"
                            />
                        </template>
                    </cute-titled-block>
                    <cute-titled-block v-if="!isJinhua" title="访问控制">
                        <template #content>
                            <el-form-item label="IP黑白名单">
                                <el-switch
                                    v-model="requestParam.control_switch"
                                    @change="black_white_list_switch_change"
                                ></el-switch>
                            </el-form-item>
                            <div v-show="showBlackWhiteList">
                                <el-form-item label="类型">
                                    <el-radio-group v-model="requestParam.control_type">
                                        <el-radio :label="2">白名单</el-radio>
                                        <el-radio :label="1">黑名单</el-radio>
                                    </el-radio-group>
                                </el-form-item>
                                <el-form-item prop="ip_list" :rules="rules.ip_list">
                                    <el-input
                                        type="textarea"
                                        v-model="requestParam.ip_list"
                                        rows="5"
                                        class="blackWhiteList"
                                    ></el-input>
                                    <div class="form-item-tip">
                                        <ct-svg-icon
                                            icon-class="info-circle"
                                            class-name="alert-icon"
                                        ></ct-svg-icon>
                                        通过黑/白名单来对访问者身份进行识别和过滤，支持IPv6地址填写。
                                    </div>
                                </el-form-item>
                            </div>
                            <!-- 支持区域访问控制 -->
                            <access-region-control
                                ref="accessRegionControlRef"
                                :defaultValue="requestParam.region_access_control"
                                @change="val => (requestParam.region_access_control = val)"
                            />
                        </template>
                    </cute-titled-block>
                    <cute-titled-block title="传递用户IP回源">
                        <template #content>
                            <el-form-item label="传递用户IP回源">
                                <el-switch v-model="requestParam.user_ip.switch"></el-switch>
                            </el-form-item>
                            <div v-if="requestParam.user_ip.switch">
                                <el-form-item label="传递用户IP回源方式">
                                    <div class="toa-link-wrapper">
                                        <el-select
                                            v-model="requestParam.user_ip.proxy_protocol"
                                            placeholder="请选择"
                                        >
                                            <el-option label="tcp_option" :value="2"></el-option>
                                            <el-option label="proxy_protocol" :value="1"></el-option>
                                        </el-select>
                                        <div class="toa-link" v-if="requestParam.user_ip.proxy_protocol == 2">
                                            <el-link
                                                type="primary"
                                                class="aocdn-ignore-link"
                                                @click="$docHelp(toaDownload)"
                                                >toa模块下载</el-link
                                            >
                                        </div>
                                    </div>
                                    <div
                                        v-show="requestParam.user_ip.proxy_protocol == 2"
                                        class="form-item-tip"
                                    >
                                        <ct-svg-icon
                                            icon-class="info-circle"
                                            class-name="alert-icon"
                                        ></ct-svg-icon>
                                        提示:使用tcp_option传递用户IP回源时,您需要下载适配源站系统版本的toa模块,并安装到源站后才可正常使用该功能。
                                    </div>
                                </el-form-item>
                                <div v-if="requestParam.user_ip.proxy_protocol == 1">
                                    <el-form-item
                                        prop="proxy_protocol_version"
                                        :rules="rules.proxy_protocol_version"
                                        label="proxy_protocol版本"
                                    >
                                        <el-select
                                            v-model="requestParam.user_ip.proxy_protocol_version"
                                            placeholder="请选择"
                                        >
                                            <el-option label="v1" :value="1"></el-option>
                                            <el-option label="v2" :value="2"></el-option>
                                        </el-select>
                                        <div class="form-item-tip">
                                            <ct-svg-icon
                                                icon-class="info-circle"
                                                class-name="alert-icon"
                                            ></ct-svg-icon>
                                            请根据源站支持的proxy_protocol协议版本选择,v1版本仅支持tcp协议,
                                            v2版本同时支持tcp和udp。
                                            <div>
                                                <span style="margin-left:20px;"></span
                                                >选择proxy_protocol协议时，必须确保源站也同时开启proxy_protocol协议；关闭时，源站也必须同时关闭。否则，业务会受到影响。
                                            </div>
                                        </div>
                                    </el-form-item>
                                </div>
                            </div>
                        </template>
                    </cute-titled-block>
                    <!-- <cute-titled-block title="DDos防护">
                    <template #content>
                        <el-form-item label="DDos防护开关">
                            <el-switch v-model="requestParam.ddos_switch"></el-switch>
                        </el-form-item>
                    </template>
                </cute-titled-block> -->
                </el-form>
            </ct-box>

            <ct-box class="ct-box-domain-create-wrapper view-box" v-show="activeStep === 1">
                <security-config ref="securityConfigRef" />
            </ct-box>
        </el-scrollbar>
        <cute-fixed-footer class="submit">
            <div class="footer-content">
                <el-button type="primary" @click="handleNextStep" :loading="isLoading">下一步</el-button>
            </div>
        </cute-fixed-footer>

        <!-- 域名归属权校验弹窗 -->
        <domain-verify-dialog
            :visible="domainVerifyCheckVisible"
            :domain="requestParam.domain"
            :checkResult="checkResult"
            @verifyResult="val => (verify_result = val)"
            @close="domainVerifyCheckVisible = false"
        />
    </ct-section-wrap>
</template>
<script>
/* eslint-disable @typescript-eslint/camelcase */
import { OverviewUrl } from "@/config/url/ipa/overview";
import { DomainUrl } from "@/config/url/ipa/domain";
import { WorkOrderList } from "@/config/url/ipa/work_order_list";
import { get } from "lodash-es";
import { ip } from "@/config/pattern";
import { cloneDeep } from "lodash-es";
import ctSvgIcon from "@/components/ctSvgIcon";
import urlTransformer from "@/utils/logic/url";
import { SecurityAbilityModule } from "@/store/modules/securityAbility";
import AccessRegionControl from "../components/access-region-control/index.vue";
import { defaultAccessRegionControlForm } from "../components/access-region-control/util";
import backSourceConf from "@/views/ipa/domain/components/backSourceConf/index.vue";
import { forwardOriginItem } from "../components/backSourceConf/templateData";
import { transFormPropToClassName } from "@/utils/utils";
import SecurityConfig from "./components/SecurityConfig.vue";
import domainVerifyDialog from "./components/domainVerifyDialog.vue";

export default {
    name: "domainAdd",
    mixins: [],
    components: { ctSvgIcon, AccessRegionControl, backSourceConf, SecurityConfig, domainVerifyDialog },
    data() {
        return {
            domainVerifyCheckVisible: false,
            checkResult: {},
            verify_result: "",
            isLoading: false, // 按钮是否加载，防止多次点击
            domain: "",
            areaScopeMap: [
                { id: 1, name: "中国内地", disabled: true },
                { id: 2, name: "全球（不含中国内地）", disabled: true },
                { id: 3, name: "全球", disabled: true },
            ],
            activeStep: 0,
            validationResult: {}, // 表单检验结果{ [prop]: valid }映射
            requestParam: {
                // ddos_switch: 0, // ddos防护开关
                area_scope: 1,
                action: 4, //4-创建，5-更新
                // 基础信息？
                access_mode: 1, // 接入方式
                domain: "",
                inst_name: "", // 实例名称
                ipv6_enable: true,
                // 回源配置？回源策略
                origin_type: 1,
                //传递用户ip
                user_ip: {
                    switch: false,
                    proxy_protocol: 1,
                    proxy_protocol_version: 1,
                },
                // 访问控制？
                control_switch: false,
                control_type: 1,
                ip_list: undefined,
                region_access_control: defaultAccessRegionControlForm(),
                // 回源配置相关
                forward_origins: [cloneDeep(forwardOriginItem)],
            },
            showBlackWhiteList: false, // 是否展示访问控制

            rules: {
                // 基本信息校验
                access_mode: [{ required: true, message: "请选择接入方式", trigger: "change" }],
                domain: [
                    { required: true, message: "请输入域名", trigger: "blur" },
                    // 支持 泛域名 *.ctyun.cn
                    {
                        trigger: ["change", "blur"],
                        pattern: /^(\*\.|\*)?([a-z0-9]([a-z0-9-_]{0,61}[a-z0-9])?\.)+[a-z0-9-]{2,32}$/,
                        message: "输入域名格式错误",
                    },
                ],
                inst_name: [
                    { required: true, message: "请输入实例名称", trigger: "blur" },
                    {
                        trigger: ["change", "blur"],
                        pattern: /^[a-zA-Z|\u4E00-\u9FA5|0-9]*$/g,
                        message: "不超过10个字符的中英文+数字",
                    },
                    {
                        max: 10,
                        message: "不超过10个字符的中英文+数字",
                        trigger: ["blur", "change"],
                    },
                ],
                // 回源策略
                origin_type: [{ required: true, message: "请选择回源策略", trigger: "blur" }],
                // 访问控制？黑白名单
                ip_list: [
                    {
                        validator: (rule, value, callback) => {
                            if (value) {
                                const reg = /^[a-zA-Z0-9,-./:\s]*$/;
                                if (reg.test(value)) {
                                    callback();
                                } else {
                                    callback(new Error(`请求端口输入有误`));
                                }
                            } else {
                                callback();
                            }
                        },
                    },
                    {
                        validator: this.checkIpList,
                        trigger: "blur",
                    },
                ],
                // 传递用户ip回源
                proxy_protocol_version: [
                    {
                        validator: this.validatorVersion,
                        trigger: "change",
                    },
                ],
            },
        };
    },
    async mounted() {
        // scc获取客户账号开通情况，仅开通应用加速（中国内地），控制台只可以开通中国内地的加速类型
        // 既开通了基础产品应用加速（中国内地）、又开通了功能产品应用加速（全球不含中国内地）;则控制台加速区域展示中国内地、全球（不含中国内地）、全球三个可供选择。
        const billingMethod = await this.getBillingMethod();
        if (billingMethod?.result?.has_product === false) {
            this.$alert("您尚未开通边缘接入产品，请完成订购后尝试新增域名。", "提示", {
                confirmButtonText: "确定",
                callback: () => {
                    this.$router.push({
                        name: "domain.list",
                    });
                },
            });
        }
        if (billingMethod?.result?.mainland === true) this.areaScopeMap[0].disabled = false;
        if (billingMethod?.result?.global === true || billingMethod?.result?.overseas === true) {
            // 加速区域若为全球/全球不含内地，其他区域也都可以选择
            this.areaScopeMap.map(area => (area.disabled = false));
        }
        // 当前选中的加速区域默认改为可选的第一个区域
        const firstValidAreaIndex = this.areaScopeMap.findIndex(area => !area.disabled);
        if (firstValidAreaIndex !== -1) {
            this.requestParam.area_scope = firstValidAreaIndex + 1;
        } else {
            this.requestParam.area_scope = null;
        }

        if (this.$route.query.domain) {
            this.domain = this.$route.query.domain;
            const { result } = await this.getDetail();
            // 传递用户ip
            const data = {
                switch: false,
                proxy_protocol: 1,
                proxy_protocol_version: 1,
            };
            const user_ip = result.config.user_ip || {};
            data.switch = user_ip.proxy_protocol === 2 && user_ip.tcp_option === 2 ? false : true;
            if (data.switch) {
                data.proxy_protocol = user_ip.proxy_protocol === 1 ? 1 : 2;
                data.proxy_protocol_version =
                    user_ip.proxy_protocol === 1 ? user_ip.proxy_protocol_version : 1;
            }
            this.requestParam = {
                area_scope: result.area_scope,
                action: 4, //4-创建，5-更新
                // 基础信息？
                domain: result.domain,
                // 回源配置？源站
                detail: result.config.origin?.detail,
                // 回源配置？回源策略
                origin_type: result.config.origin?.origin_type,
                // 回源配置？端口信息
                tcp_ports: result.config.origin?.tcp_ports,
                udp_ports: result.config.origin?.udp_ports,
                // 访问控制？
                control_switch: result.config.access_control?.control_switch === 1,
                control_type: result.config.access_control?.control_type || 1,
                ip_list: result.config.access_control?.ip_list,
                ipv6_enable: result.ipv6_enable,
                user_ip: data,
                region_access_control: defaultAccessRegionControlForm(),
            };
            this.showBlackWhiteList = result.config.access_control?.control_switch === 1;
        }
        document
            .querySelector(".blackWhiteList textarea")
            ?.setAttribute(
                "placeholder",
                "支持输入单个IP，多个IP使用逗号隔开；\n支持输入IP段，例如：***********-***********00；\n支持输入IP掩码，例如：***********/22。"
            );
    },
    computed: {
        isJinhua() {
            // TODO 0619
            return SecurityAbilityModule.serviceIdentifier === "jinhua";
        },
        newobjct() {
            return JSON.parse(JSON.stringify(this.requestParam.config.origin.detail));
        },
        isInternational() {
            return window.location.href.includes("esurfingcloud.com");
        },
        virtual_domain() {
            let data = 0;
            if (this.requestParam.access_mode === 2) {
                data = 1;
            }
            return data;
        },
        toaDownload() {
            return urlTransformer({
                a1Ctyun: "https://www.ctyun.cn/document/10065985/10323895",
                a1Ctcloud: "https://www.esurfingcloud.com/document/zh-cn/20689737/20690458",
            });
        },
    },
    methods: {
        access_mode_change() {
            this.requestParam.domain = "";
            this.requestParam.inst_name = "";

            this.$nextTick(() => {
                this.$refs.form.validate();
            });
        },
        black_white_list_switch_change(val) {
            this.$set(this.requestParam, "ip_list", undefined);
            this.showBlackWhiteList = !this.showBlackWhiteList;
        },
        onValidate(prop, valid) {
            this.validationResult[prop] = valid;
        },
        // toaDownload() {
        //     if (window.location.href.includes("esurfingcloud.com")) return;
        //     window.open("https://www.ctyun.cn/document/10065985/10323895", "_blank");
        // },
        async domainBlur(val) {
            if (val && this.validationResult.domain) {
                this.verify_result = "";
                const result = await this.getRepeatVerify(val);
                const { main_domain_verify } = result.result;
                if (main_domain_verify === false) {
                    const checkVerify = this.domainVerifyCheck(result.result);
                    if (!checkVerify) return;
                    this.domainVerifyCheckVisible = true;
                    this.checkResult = result.result;
                }
            }
        },
        domainVerifyCheck(result) {
            const { verify_code, verify_desc } = result;
            if (verify_code === "BLACKLIST") {
                this.verify_result = verify_desc || "该域名存在历史违规行为记录，目前不允许接入";
                return false;
            }

            if (verify_code === "TOP_ERROR") {
                this.verify_result = verify_desc || "顶级域名错误";
                return false;
            }

            return true;
        },
        getRepeatVerify(val) {
            if (this.requestParam.access_mode === 2) return;
            return this.$ctFetch(DomainUrl.domainRepeatVerify, {
                method: "GET",
                transferType: "json",
                data: {
                    domain: val,
                },
            });
        },
        // 获取域名详情
        getDetail() {
            return this.$ctFetch(WorkOrderList.workOrderDetail, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                    domain: this.$route.query.domain,
                    id: this.$route.query.id,
                },
            });
        },
        // 参数转化
        restructureData(reqParam, backSourceConfData) {
            const param = {
                area_scope: reqParam.area_scope,
                account_id: this.$store.state.user.userInfo.userId,
                action: reqParam.action, //4-创建，5-更新
                // 基本信息
                domain: reqParam.domain,
                access_mode: reqParam.access_mode,
                inst_name: reqParam.inst_name,
                virtual_domain: this.virtual_domain,
                ipv6_enable: reqParam.ipv6_enable,
                config: {
                    // 源站信息
                    origin: {
                        // 源站
                        // detail: reqParam.detail,
                        // 回源策略
                        origin_type: parseInt(reqParam.origin_type),
                        // 回源配置相关
                        forward_origins: backSourceConfData, // 函数内部已深拷贝对象
                        // 端口信息
                        // tcp_ports: reqParam.tcp_ports.filter(itm => itm.origin || itm.req),
                        // udp_ports: reqParam.udp_ports.filter(itm => itm.origin || itm.req),
                        probe_port: undefined,
                    },
                    // 访问控制
                    access_control: {
                        control_switch: reqParam.control_switch ? 1 : 2,
                        control_type: parseInt(reqParam.control_type),
                        ip_list: reqParam.ip_list,
                        region_access_control: this.$refs.accessRegionControlRef?.formData || null,
                    },
                    //传递用户ip
                    user_ip: reqParam.user_ip.switch
                        ? {
                              proxy_protocol: reqParam.user_ip.proxy_protocol === 1 ? 1 : null,
                              tcp_option: reqParam.user_ip.proxy_protocol === 2 ? 1 : null,
                              proxy_protocol_version:
                                  reqParam.user_ip.proxy_protocol === 1
                                      ? reqParam.user_ip.proxy_protocol_version
                                      : null,
                          }
                        : null,
                },
                detail: 1,
            };
            return param;
        },

        handleNextStep() {
            // 域名配置校验
            if (this.activeStep === 0) {
                console.log("test");
            }
        },

        // 新增接入
        async addDomain(val) {
            const pre_verify_result = this.verify_result;
            this.verify_result = "";
            try {
                await this.$refs.form.validate();
            } catch (err) {
                this.verify_result = pre_verify_result;
                this.handleFormError(err);
                return;
            }

            if (val?.domain || val?.inst_name) {
                this.isLoading = true;

                // 验证域名是否重复
                let result = null;
                try {
                    result = await this.getRepeatVerify(val?.domain);
                } catch (err) {
                    this.$errorHandler(err);
                    this.isLoading = false;
                    return;
                }

                // 域名未通过验证
                if (this.requestParam.access_mode === 1 && result?.result?.main_domain_verify === false) {
                    const checkVerify = this.domainVerifyCheck(result.result);
                    if (!checkVerify) {
                        this.$refs.form.validateField("domain");
                        return;
                    }
                    this.domainVerifyCheckVisible = true;
                    this.domain_zone = result.result.domain_zone;
                    this.zone_record = result.result.zone_record;
                    this.tableData.push({
                        zone_type: result.result.zone_type,
                        zone_host: result.result.zone_host,
                        zone_record: result.result.zone_record,
                    });
                    // 域名通过验证, 提交工单
                } else if (
                    this.requestParam.access_mode === 2 ||
                    result?.result?.main_domain_verify === true
                ) {
                    const backSourceConfData = this.$refs.backSourceConf.getSubmitData();
                    const hasFtpData = backSourceConfData.some(item => {
                        const ftpPorts = get(item, "ftp_ports");
                        return ftpPorts && Object.values(ftpPorts).every(value => value);
                    });

                    // 产品需求，检查是否配置ftp值
                    if (hasFtpData) {
                        try {
                            await this.$alert(
                                "仅支持被动模式的ftp，不支持主动模式的ftp，如您确认是被动模式的ftp，请点击【确定】后继续",
                                "提示",
                                {
                                    confirmButtonText: "确定",
                                    type: "warning",
                                }
                            );
                        } catch (e) {
                            this.isLoading = false;
                            return;
                        }
                    }

                    const param = this.restructureData(this.requestParam, backSourceConfData);
                    if (this.requestParam.access_mode === 1) {
                        delete param?.inst_name;
                    } else {
                        delete param?.domain;
                    }

                    try {
                        await this.reqAddDomain(param);
                        /**
                        const query = {
                            // ***domain+status获取工单列表
                            domain: this.requestParam.domain,
                            status: 2,
                            isAdd: true,
                            // ***一个域名只会有一个正在操作中的工单
                            // ***domain+status获取详情（新增/编辑后跳转工单详情）
                        };
                        if (this.$route.query.isReBuild) {
                            query.isReBuild = this.$route.query.isReBuild;
                        }
                    */
                        // ***新增/编辑后跳转到域名列表页
                        this.$message.success("新增接入成功，等待一段时间后生效");
                        this.$router.push({
                            name: "domain.list",
                            // query: query,
                        });
                    } catch (e) {
                        this.$errorHandler(e);
                        this.isLoading = false;
                    }
                }
            }
        },
        /**
         * 处理表单报错情况
         */
        handleFormError(errorMap) {
            // 只处理回源配置的报错情况，由于新版回源配置的内容比较多，且能添加组
            // 这里优化补充，当回源配置报错时，自动定位到报错点，报错原因
            const errors = Object.keys(errorMap);
            for (const error of errors) {
                // 非回源配置相关，提前跳出
                if (error.indexOf("forward_origins") === -1) {
                    continue;
                }

                // 命中一个，则退出循环，后续循环不执行
                // 能够找到对应元素的原因是，前端在这里取巧，刚好利用各个元素的prop绑定成对应的className
                // 所以报错的prop可以找到对应的dom元素，进而滚动到对应的报错元素
                const className = transFormPropToClassName(error);
                const errorEle = document.querySelector(`.${className}`);
                errorEle && errorEle.scrollIntoView({ behavior: "smooth" });
                break;
            }
        },
        /**
         * 检查多个IP
         */
        checkIpList(rule, value, callback) {
            if (this.showBlackWhiteList && !value) {
                return callback(new Error("IP黑白名单开启，请填写列表"));
            }
            if (!this.showBlackWhiteList && !value) {
                return callback();
            }

            const ary = value.split(",");
            // ip检验规则
            const pattern = new RegExp(ip);
            const numberPattern = /^([1-9][0-9]?|[1][0-9][0-9]|[2]([0-4][0-9]|[5][0-6]))$/;
            const res = ary.every(item => {
                // 单个ip的情况
                if (pattern.test(item)) {
                    return true;
                }

                // ip掩码情况
                const maskCode = item.split("/");
                if (maskCode && maskCode.length === 2) {
                    const start = maskCode[0];
                    const end = maskCode[1];
                    if (pattern.test(start) && numberPattern.test(end)) {
                        return true;
                    }
                }

                // 区间情况
                const range = item.split("-");
                if (range && range.length === 2) {
                    const start = range[0];
                    const end = range[1];
                    if (pattern.test(start) && pattern.test(end)) {
                        return true;
                    }
                }

                return false;
            });
            if (!res) {
                return callback(new Error("格式有误，请检查"));
            }

            return callback();
        },
        /**
         * 传递用户ip回源
         */
        validatorVersion(rule, value, callback) {
            // 检查是否有udp端口值
            const forwards_origin = get(this.requestParam, "forward_origins", []) || [];
            const hasUdpPortValue = forwards_origin.some(item => {
                const udp_ports = item.udp_ports || [];
                return udp_ports.some(valueItem => valueItem.req);
            });

            if (hasUdpPortValue) {
                if (
                    this.requestParam.user_ip.proxy_protocol === 1 &&
                    this.requestParam.user_ip.proxy_protocol_version === 1 &&
                    this.requestParam.user_ip.switch
                ) {
                    return callback(new Error("v1版本仅支持tcp协议"));
                }
            }
            return callback();
        },
        reqAddDomain(data) {
            return this.$ctFetch(DomainUrl.addDomain, {
                method: "POST",
                transferType: "json",
                clearEmptyParams: false,
                body: {
                    data,
                },
            });
        },
        getBillingMethod() {
            return this.$ctFetch(OverviewUrl.domainBillingCheck, {
                method: "GET",
                transferType: "json",
                data: {
                    account_id: this.$store.state.user.userInfo.userId,
                },
            });
        },
    },
};
</script>
<style lang="scss" scoped>
.domain-create-wrapper ::v-deep .el-scrollbar {
    margin-bottom: 0 !important;
}
.form-item-tip {
    font-size: 12px;
    color: #7c818c;
    line-height: 18px;
    font-weight: 400;
    margin-top: 4px;
    &.strong {
        color: red;
    }
}
.alert-icon {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}

.toa-link-wrapper {
    display: flex;
    .toa-link {
        margin-left: 8px;
        width: 100px;
    }
}
.submit {
    margin-top: 200px;
    z-index: 200;
    .footer-content {
        display: flex;
        justify-content: flex-end;
    }
}
.icon-column-label1 {
    color: $text-color-light;
    margin-right: $margin;
    font-size: $text-size-md;
    line-height: 18px;
    height: 14px;
    width: 14px;
}
.step-wrapper {
    width: 1000px;
    margin: 0 auto 20px;
}
</style>
