si'ya哦<template>
    <el-dialog
        :title="$t('certificate.PEM编码参考样例')"
        :visible.sync="visible"
        :close-on-click-modal="false"
        append-to-body
        width="700px"
        class="pem-sample-dialog"
    >
        <div class="sample-content">
            <!-- 标准证书公钥样例 -->
            <div v-if="sampleType === 'publicKey'">
                <div class="sample-section">
                    <div class="code-block">
                        <pre>{{ standardPublicKey }}</pre>
                    </div>
                </div>
            </div>
            <!-- 标准证书私钥样例 -->
            <div v-if="sampleType === 'privateKey'">
                <div class="sample-section">
                    <div class="code-block">
                        <pre>{{ standardPrivateKey }}</pre>
                    </div>
                </div>
            </div>
            <!-- SM2证书公钥样例 -->
            <div v-if="sampleType === 'sm2PublicKey'">
                <div class="sample-section">
                    <div class="code-block">
                        <pre>{{ sm2PublicKey }}</pre>
                    </div>
                </div>
            </div>
            <!-- SM2证书私钥样例 -->
            <div v-if="sampleType === 'sm2PrivateKey'">
                <div class="sample-section">
                    <div class="code-block">
                        <pre>{{ sm2PrivateKey }}</pre>
                    </div>
                </div>
            </div>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button @click="close">{{ $t("common.dialog.close") }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: "PemSampleDialog",
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        showSM2: {
            type: Boolean,
            default: true,
        },
        sampleType: {
            type: String,
            default: "publicKey", // 'publicKey', 'privateKey', 'sm2PublicKey', 'sm2PrivateKey'
            validator: value => ["publicKey", "privateKey", "sm2PublicKey", "sm2PrivateKey"].includes(value),
        },
    },
    data() {
        return {
            activeTab: this.sampleType,
        };
    },
    watch: {
        sampleType(newVal) {
            this.activeTab = newVal;
        },
    },
    computed: {
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit("input", val);
            },
        },
        // 生成固定的标准证书公钥样例
        standardPublicKey() {
            return this.generateStandardPublicKey();
        },
        // 生成固定的标准证书私钥样例
        standardPrivateKey() {
            return this.generateStandardPrivateKey();
        },
        // 生成固定的SM2证书公钥样例
        sm2PublicKey() {
            return this.generateSM2PublicKey();
        },
        // 生成固定的SM2证书私钥样例
        sm2PrivateKey() {
            return this.generateSM2PrivateKey();
        },
    },
    methods: {
        close() {
            this.visible = false;
        },

        // 生成标准证书公钥样例
        generateStandardPublicKey() {
            const base64Content = this.generateRandomBase64(580);
            const base64Content2 = this.generateRandomBase64(580);
            const formattedContent = this.formatBase64(base64Content);
            const formattedContent2 = this.formatBase64(base64Content2);
            return `-----BEGIN CERTIFICATE-----\n${formattedContent}\n-----END CERTIFICATE-----\n-----BEGIN CERTIFICATE-----\n${formattedContent2}\n-----END CERTIFICATE-----`;
        },
        // 生成标准证书私钥样例
        generateStandardPrivateKey() {
            const base64Content = this.generateRandomBase64(1100);
            const formattedContent = this.formatBase64(base64Content);
            return `-----BEGIN PRIVATE KEY-----\n${formattedContent}\n-----END PRIVATE KEY-----`;
        },
        // 生成SM2证书公钥样例
        generateSM2PublicKey() {
            const base64Content = this.generateRandomBase64(580);
            const base64Content2 = this.generateRandomBase64(580);
            const formattedContent = this.formatBase64(base64Content);
            const formattedContent2 = this.formatBase64(base64Content2);
            return `-----BEGIN CERTIFICATE-----\n${formattedContent}\n-----END CERTIFICATE-----\n-----BEGIN CERTIFICATE-----\n${formattedContent2}\n-----END CERTIFICATE-----`;
        },
        // 生成SM2证书私钥样例
        generateSM2PrivateKey() {
            const base64Content = this.generateRandomBase64(180);
            const formattedContent = this.formatBase64(base64Content);
            return `-----BEGIN PRIVATE KEY-----\n${formattedContent}\n-----END PRIVATE KEY-----`;
        },
        // 生成随机Base64内容
        generateRandomBase64(length) {
            const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
            let result = "";

            for (let i = 0; i < length; i++) {
                result += chars[Math.floor(Math.random() * chars.length)];
            }

            return result;
        },
        // 格式化Base64内容，每64个字符换行
        formatBase64(base64) {
            return btoa(base64)
                .match(/.{1,64}/g)
                .join("\n");
        },
    },
};
</script>

<style lang="scss" scoped>
.pem-sample-dialog {
    .sample-content {
        .sample-section {
            margin-bottom: 20px;

            h4 {
                margin: 0 0 10px 0;
                color: #303133;
                font-size: 14px;
                font-weight: 600;
            }

            .code-block {
                background-color: #f5f7fa;
                border: 1px solid #e4e7ed;
                border-radius: 4px;
                padding: 12px;

                pre {
                    margin: 0;
                    font-family: "Courier New", Courier, monospace;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #606266;
                    white-space: pre-wrap;
                    word-break: break-all;
                }
            }
        }
    }

    ::v-deep .el-tabs__content {
        padding: 20px 0;
    }

    ::v-deep .el-tab-pane {
        max-height: 400px;
        overflow-y: auto;
    }
}
</style>
