// 当需要扩展 vue 声明时，需要在顶端引入 Vue
import urlTransformer from "@/utils/logic/url";
import Vue, { VNode } from "vue";

declare global {
    namespace JSX {
        // eslint-disable-next-line prettier/prettier
        interface Element extends VNode { }
        // eslint-disable-next-line prettier/prettier
        interface ElementClass extends Vue { }
        interface IntrinsicElements {
            [elem: string]: any;
        }
    }
}

// 补充需要声明的类型，基于 TS 的模块补充 (module augmentation) 特性
declare module "vue/types/vue" {
    // 声明实例的 property
    interface Vue {
        // 此时可以使用 new Vue().$ctFetch ，或是组件中的 this.$ctFetch
        $ctFetch: <T>(url: string, options?: CtFetchOptions) => Promise<T>;
        $errorHandler: Function;
        $ctUtil: {
            [util: string]: Function;
        };
        $ctBus: Vue;
        $urlTransformer: typeof urlTransformer;
        $PoweredByQiankun: boolean;
        $docHelp: (url: string) => void;

        // 全局filter声明
        convertBandwidthM2P: (scale: number) => any;
        convertFlowM2P: (scale: number) => any;
        convertBandwidthB2P: (scale: number) => any;
        convertFlowB2P: (scale: number) => any;
        convertBandwidthB2P: (scale: number) => any;
        convertTenThousand2Int: (content: string) => any;
    }
    // 声明全局的 property
    interface VueConstructor {
        // 此时可以使用 Vue.$myGlobal
        // $myGlobal: string;
    }
}

// TODO 待定
declare module "vue-router/types/route" {
    // interface RouteConfig {
    //     meta: {
    //         breadcrumb: {
    //             title: string;
    //             route: string[];
    //         };
    //     };
    // }
}

declare module "vuex" {
    interface Store {
        $message: Function;
        $errorHandler: Function;
    }
}
