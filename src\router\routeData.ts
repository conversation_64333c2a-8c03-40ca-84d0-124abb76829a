import Vue from "vue";
import { get } from "lodash-es";
import httpRoutes from "./microAppRoutes";

// 微应用路由
export const microAppRoutes = [
    {
        path: "/aocdnMicroApp",
        name: "appVue",
        component: () => import("./index.vue"),
        redirect: { path: "/aocdnMicroApp/index" },
        children: [],
    },
    // 任意路由
    { path: "*", redirect: "/aocdnMicroApp" },
    // 默认路由
    { path: "/", redirect: { path: "/aocdnMicroApp/index" } },
    // 辅助跳转，可以配合 refreshCurrentRoute 刷新当前路由
    {
        name: "redirect",
        path: "/redirect",
        component: Vue.component("CtRedirect"),
    },
];
// 直接访问路由
export const normalAppRoute = [
    // 任意路由
    { path: "*", redirect: "/" },
    // 默认路由
    { path: "/", redirect: { path: "/authLoading" } },
    ...httpRoutes,
    // 辅助跳转，可以配合 refreshCurrentRoute 刷新当前路由
    {
        name: "redirect",
        path: "/redirect",
        component: Vue.component("CtRedirect"),
    },
];

export const prefix = (window as any).__POWERED_BY_QIANKUN__ ? "/aocdnMicroApp" : "";

/**
 * 设置路由，防止路由内部children中还存在/开头的path，导致实际为扁平路由
 * @param arr
 */
export function setRoutes(arr: any) {
    for (const item of arr) {
        // 是否跳过微应用前缀,使得该路由不需要添加微应用前缀也可进行访问
        if (get(item, "meta.isSkipMicroApp")) {
            continue;
        }

        item.path = item.path.replace("/", "");
        if (item.children) {
            setRoutes(item.children);
        }
    }
}
