<template>
    <ct-section-wrap v-loading="loading"> </ct-section-wrap>
</template>

<script lang="ts" setup>
import { AppModule } from "@/store/modules/app";
import { MenuModule } from "@/store/modules/menu";
import { computed, getCurrentInstance, watch } from "vue";

const instance = getCurrentInstance();
const { proxy } = instance || {};

const loading = computed(() => {
    return AppModule.contenProductLoading || AppModule.contentMenuLoading;
});

watch(
    loading,
    newVal => {
        if (newVal) return;
        if (!MenuModule.menuList.length) {
            proxy?.$router.push({ name: "forbidden", query: { type: "auth" } });
            return;
        }

        // 默认跳转到首页
        const nIndex = MenuModule.menuList.find(item => item.ucode === "nindex");
        if (nIndex) {
            proxy?.$router.push({
                path: (nIndex?.hrefLocal || nIndex?.items?.[0].hrefLocal)?.replace("#", "") || "",
            });
            return;
        }

        // 如果首页不存在，则跳转到第一个菜单
        const firstMenu = MenuModule.menuList[0];
        if (firstMenu) {
            const path = (firstMenu?.hrefLocal || firstMenu?.items?.[0].hrefLocal)?.replace("#", "") || "";
            proxy?.$router.push({ path });
        } else {
            proxy?.$router.push({ name: "forbidden", query: { type: "auth" } });
        }
    },
    { immediate: true }
);
</script>
