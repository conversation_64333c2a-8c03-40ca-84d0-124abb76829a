{"title": "Billing Details", "cdn": {"title": "CDN", "ondemandOderBtn": "Activate CDN Service", "ondemandOderTip": "Reminder: You haven't activated CDN service with on-demand billing, to avoid service outage, please activate CDN service asap.", "packageOderBtn": "Subscribe to CDN Package"}, "icdn": {"title": "Integrated CDN", "ondemandOderBtn": "Activate ICDN Service", "ondemandOderTip": "Reminder: You haven't activated ICDN service with on-demand billing, to avoid service outage, please activate ICDN service asap.", "packageOderBtn": "Subscribe to ICDN Package"}, "detailTitle": ["My PAYG Services", "My Packages"], "detailTip": ["[Notes]", "1. Only the product plans in service or to be effective will be displayed on this page;", "2. Service reducing is temporarily not available, for example, removing or disabling Premium Network Service or Content Audit, shrinking the acceleration region from Global to only Chinese mainland;", "2. The billing method of on-demand basic services can only be switched between \"Traffic\" and \"Daily Peak Bandwidth\", and the effective time will be at 00:00 of the next day. You can do multiple changes before the effective time, and only the last change will take effect;", "{idx}. The billing method of each billing region of Global(excluding Chinese mainland) should be identical.", "Validity: One year validity since purchase. Expired packages can not be used for usage deduction. To ensure business stability, please buy new package before existing packages expire. ", "Unsubscription/Refund: Within the validity period, the package with surplus can be unsubscribed to get a partial refund and can't be forwared to other packages.", "Restrictions: \n1. You need to activate basic service with on-demand billing first before buying a package.\n2. Please change the billing method from \"Daily Peak Bandwidth\" to \"Traffic\" in advance if you want to buy a traffic package, otherwise, the purchased traffic package will be locked. The validity of the traffic package will not be extended when it is being locked.\n3. The packages of different billing regions of Global(excluding Chinese mainland) can be used for usage deduction within the same billing region, cannot be used across different billing regions.", "4. After submitting the changes for billing method, the pending entries to be effective will be shown on the page within 2~3 minutes.", "2. Service reducing is temporarily not available, for example, removing or disabling Premium Network Service, shrinking the acceleration region from Global to only Chinese mainland;"], "detailTipNew": {"tip1": "1. Only the product plans in service or to be effective will be displayed on this page.", "tip2": "2. The billing method of on-demand basic services can only be switched between \"Traffic\" and \"Bandwidth\". The current page only allows switching between \"Traffic\" and \"Daily Peak Bandwidth\", and the change will take effect at 00:00 of the next day. The billing method must remain consistent across different billing regions in Global (excluding the Chinese mainland). Before the change takes effect, you may perform multiple modifications, with only the latest one being applied. After the billing method change is submitted, it may take 2–3 minutes for the pending change record to appear on the page.", "tip3": "3. When the basic service of Global(excluding Chinese mainland) is in status of \"In service\" and any of the billing region of Global(excluding Chinese mainland) has packages of \"In use\" or \"Locked\" status, then you can not change the acceleration region or unsubscribe from the basic service of the same product. If you need to do so, please unsubscribe the packages of Global(excluding Chinese mainland) in advance."}, "detailTipCtcloud": {"tip1": "1. Only the product plans in service or to be effective will be displayed on this page.", "tip2": "2. The billing method of on-demand basic services can only be switched between \"Traffic\" and \"Bandwidth\". The current page only allows switching between \"Traffic\" and \"Daily Peak Bandwidth\", and the change will take effect at 00:00 of the next day. The billing method must remain consistent across different billing regions in Global (excluding the Chinese mainland). Before the change takes effect, you may perform multiple modifications, with only the latest one being applied. After the billing method change is submitted, it may take 2–3 minutes for the pending change record to appear on the page."}, "ondemandOderTable": {"columns": ["Acceleration Type", "Acceleration Region", "Service Type", "Product Details", "Validity", "Status", "Operation"], "billingArea": ["Chinese mainland", "Global (excluding Chinese Mainland)", "Global"], "serverType": ["Basic Service", "Value-added Service"], "status": ["In service", "To be effective"]}, "packageOderTable": {"columns": ["Package Type", "Applicable Scope", "Unused Volume", "Validity", "Status", "Operation"], "label": ["Unused {0}", "Effective Time", "Expiration Time", "To be expired"], "billingArea": ["Chinese mainland", "Global (excluding Chinese Mainland)"], "expireInfo": ["Your service might be impacted if your service with on-demand billling got expired. To avoid service outage, please confirm whether you have any other valid services.", "The package validity is one year since purchase.To avoid service outage, please confirm whether you have any other valid packages or activated basic service available."], "status": ["Not used", "In use", "Traffic exhausted", "Expired", "Terminated", "Unsubscribed", "Locked"]}, "tableOperate": ["Unsubscribe", "Change billing method", "Change acceleration region", "Add Content Audit", "Add Premium Network Service"], "dialog": {"title": "Change billing method", "tip": ["1. The billing method of on-demand basic services can only be switched between \"Traffic\" and \"Daily Peak Bandwidth\", and the effective time will be at 00:00 of the next day. You can do multiple changes before the effective time, and only the last change will take effect. If the subscribed billing method is other than “Traffic” or “Daily Peak Bandwidth”, please go the corresponding platform for changing the billing method.", "2. The billing method of each billing region of Global(excluding Chinese mainland) should be identical, which means changing the billing method of anyone of the regions, it will be applied to the rest of them simultaneously. "], "cancel": "Cancel", "confirm": "OK", "label": ["Chinese mainland", "Global (excluding Chinese Mainland)", "Premium Network Service"], "billingTypes": ["Traffic", "Daily Peak Bandwidth", "Billing by number of image", "Bandwidth"]}, "overseaRegionName": {"201": "NA", "202": "EU", "203": "APAC-1", "204": "APAC-2", "205": "APAC-3", "206": "MEA", "207": "SA"}, "billingTypeName": {"TRAFFIC": "Traffic", "DAY_PEAK": "Daily Peak Bandwidth", "CONTENT_AUDIT_D_C": "Billing by number of images", "TP95-VPL": "Bandwidth", "TP95": "Monthly 95 percentile bandwidth", "TP1": "Monthly Peak Bandwidth", "MTH_AVG_DAY_PEAK": "Monthly average of daily peak bandwidth", "MTH_AVG_DAY_TP95": "Monthly average of daily 95 percentile bandwidth", "STAQUIC_REQ": "Number of static QUIC request", "STATIC_REQUEST": "Number of static HTTP request", "TP4": "Monthly 4th Peak Bandwidth"}, "productName": {"CDN_ACCE": "Chinese Mainland", "CDN_STAHTTPS_REQ": "Number of static HTTPS request_Chinese Mainland", "CDN_STAQUIC_REQ": "Number of static QUIC request_Chinese Mainland", "CDN_ACCE_ABROAD": "Global (excluding Chinese Mainland)", "CDN_STAHTTPS_REQ_A": "Number of static HTTPS request_Global (excluding Chinese Mainland)", "CDN_STAQUIC_REQ_A": "Number of static QUIC request_Global (excluding Chinese Mainland)", "CDN_ACCE_C_AUDIT": "Content Audit", "CDN_ACCE_VPL_A": "Premium Network Service", "CDN_ACCE_FS": "BosonFaaS", "CDN_ACCE_FS_10MS": "BosonFaaS-10ms", "CDN_ACCE_FS_50MS": "BosonFaaS-50ms", "CDN_ACCE_FS_100MS": "BosonFaaS-100ms", "DOWNLOAD_ACCE": "Chinese Mainland", "DOWNLOAD_ACCE_ABROAD": "Global (excluding Chinese Mainland)", "DL_STAQUIC_REQ": "Number of static QUIC request_Chinese Mainland", "DL_STAHTTPS_REQ": "Number of static HTTPS request_Chinese Mainland", "DL_STAQUIC_REQ_A": "Number of static QUIC request_Global (excluding Chinese Mainland)", "DL_STAHTTPS_REQ_A": "Number of static HTTPS request_Global (excluding Chinese Mainland)", "DOWNLOAD_C_AUDIT": "Content Audit", "STATIC_ACCE": "Chinese Mainland", "STATIC_ACCE_ABROAD": "Global (excluding Chinese Mainland)", "ST_STAHTTPS_REQ": "Number of static HTTPS request_Chinese Mainland", "STATIC_STAQUIC_REQ": "Number of static QUIC request_Chinese Mainland", "ST_STAHTTPS_REQ_A": "Number of static HTTPS request_Global (excluding Chinese Mainland)", "STATIC_STAQUIC_REQ_A": "Number of static QUIC request_Global (excluding Chinese Mainland)", "STATIC_ACCE_C_AUDIT": "Content Audit", "VOD_ACCE": "Chinese Mainland", "VOD_ACCE_ABROAD": "Global (excluding Chinese Mainland)", "VOD_STAHTTPS_REQ": "Number of static HTTPS request_Chinese Mainland", "VOD_STAHTTPS_REQ_A": "Number of static HTTPS request_Global (excluding Chinese Mainland)", "VOD_STAQUIC_REQ": "Number of static QUIC request_Chinese Mainland", "VOD_STAQUIC_REQ_A": "Number of static QUIC request_Global (excluding Chinese Mainland)", "VOD_ACCE_C_AUDIT": "Content Audit", "ICDN": "Chinese Mainland", "ICDN_UPLOAD": "Upload acceleration", "ICDN_WEBSOCKET": "websocket acceleration", "ICDN_STAHTTPS_REQ": "Static HTTPS request", "ICDN_DYN_REQ": "Number of dynamic requests", "ICDN_ABROAD": "Global (excluding Chinese Mainland)", "ICDN_VPL_ABROAD": "Premium Network Service", "ICDN_FS": "BosonFaaS", "ICDN_FS_10MS": "BosonFaaS-10ms", "ICDN_FS_50MS": "BosonFaaS-50ms", "ICDN_FS_100MS": "BosonFaaS-100ms", "DOWNLOAD_ACCE_FS": "BosonFaaS", "DOWNLOAD_ACCE_FS_10MS": "BosonFaaS-10ms", "DOWNLOAD_ACCE_FS_50MS": "BosonFaaS-50ms", "DOWNLOAD_ACCE_FS_100MS": "BosonFaaS-100ms", "STATIC_ACCE_FS": "BosonFaaS", "STATIC_ACCE_FS_10MS": "BosonFaaS-10ms", "STATIC_ACCE_FS_50MS": "BosonFaaS-50ms", "STATIC_ACCE_FS_100MS": "BosonFaaS-100ms", "VOD_ACCE_FS": "BosonFaaS", "VOD_ACCE_FS_10MS": "BosonFaaS-10ms", "VOD_ACCE_FS_50MS": "BosonFaaS-50ms", "VOD_ACCE_FS_100MS": "BosonFaaS-100ms"}, "【退款说明】资源包仅支持在购买时长≤7天且未进行任何使用的情况下申请退款，除此之外均不符合退款条件，不予以退款。": "Refund Policy: Resource packages are refundable only if unused and within 7 days of purchase. Refunds are not available under any other conditions."}