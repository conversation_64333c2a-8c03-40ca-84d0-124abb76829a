{"update": "Update", "add": "Add", "disable": "Disable", "enable": "Enable", "or": " or ", "operate": "Actions", "view": "View", "edit": "Edit", "more": "More", "delete": "Delete", "modify": "Modify", "enter": "Enter", "cancel": "Cancel", "submit": "Submit", "submit1": "Submit", "type": "Type", "back": "Back", "submit2": "Submit and Save", "list": {"title": "Domain List", "titleTip": "Display enabled and disabled domain names. To add, enable, or disable a domain name, configure relevant settings.", "createBtn": "Add Domain Name", "batchCreateBtn": "<PERSON><PERSON> Add", "batchEditBtn": "Batch Modify", "allTypes": "All Types", "placeholder1": "Search domain names by keyword", "status": ["All Status", "Enabled", "Disabled", "Configuring"], "actions": ["Disable", "Enable", "Delete", "Add", "Update"], "refresh": "Refresh", "reset": "Reset", "export": "Export", "tableTip1": "This domain name is a CNAME that is used to map the accelerated domain name to the address of a CDN POP. View ", "tableTip2": "Configuration Guide", "tableLabel1": "No.", "tableLabel2": "Domain Name", "tableLabel3": "Acceleration Type", "tableLabel4": "Accelerated Region", "tableLabel5": "Status", "tableLabel6": "Created At", "tableLabel7": "Label", "addLabel": "Add", "tip1": "The content audit service change ticket you submitted is in progress.", "tip2": "The {action} ticket you submitted is in progress. ", "label1": "Domain Name", "label2": "Confirm to Disable", "label3": "Reason", "tip3": "Enter a reason (up to 128 characters)", "tip4": "Select the domain names you want to modify by batch. You can modify up to {domainCountLimit} domain names at a time.", "tip5": "Unsupported operation.  Currently, you can only batch modify domain names added for web  acceleration or VOD acceleration.", "tip6": "More than one acceleration type exists for the selected domains. You can only modify domain names of the same acceleration type in a batch.", "tip7": "Please select domain names that are editable.", "tip8": "Up to {domainCountLimit} domain names can be modified at a time.", "downloadStr": "No.,Domain Name,CNAME,Acceleration Type,Status,Creation Time,Origin Server,Level,Weight,Specify on Origin Host,HTTP Origin Port,HTTPS Origin Port,Request Host,Follow Request Port", "tip9": "The configuration of this domain name is used by multiple products. This operation will modify the configuration for all of these products. Please click Confirm to continue.", "tip10": "You have an ongoing ticket. The {title} operation is currently not allowed.", "tip11": "Caution! Please enter the domain name you want to delete: {domain}.", "tip12": "Invalid domain name.", "tip13": "The current domain stops accelerating and is disabled", "tip14": "The current domain name cannot be enabled. Please order a traffic package or activate a pay-as-you-go subscription, and try again.", "tip15": "The product is {status}, Please resume the product and then re-enable the domain name.", "tip16": "No ICP filing information of the domain name is detected. A second-round review will be conducted within 24 hours. Domain names that are not filed will be banned. To avoid affecting your CDN service,  please apply for the ICP filing at the earliest opportunity.", "tip16-1": "Verify ICP Filing", "tip17": "Your domain name is not ICP filed, so the CDN service is unavailable. If you have any questions, please ", "tip17-1": "submit a ticket ", "tip18": "Are you sure to {title} this domain name? Please operate with caution.", "tip20": "The CDN service is not activated, or the traffic package runs out. Please activate the service or order a new traffic package before adding the domain name.", "tip19": "The request to {title} the domain name is sent.", "note": "Note", "opennote": "Enabling Prompt", "remainsopen": "Enable Anyway", "downloadName": "DomainList", "tip21": "After disabling the domain, the eSurfing Cloud access layer CNAME will immediately resolve to an inaccessible address. If the domain you disabled is still resolving to eSurfing Cloud CNAME, please promptly redirect the domain to a normal, serviceable address or re-enable the eSurfing Cloud CDN service to avoid any impact on your business due to this operation.", "tableLabelipv6": "IPv6 DNS", "tableLabelipv6Tooltip": "Supports IPv6 DNS and provides IPv6 dual stack node service after being enabled", "tip22": "The CDN acceleration system or coefficient is inconsistent with the original product. Are you sure you want to change the acceleration type to CDN? Changing the acceleration type may affect billing. Please proceed with caution.", "tableLabelPremiumNetwork": "Premium Network Service", "tableLabelPremiumNetworkScene": "Premium Network Service scenarios", "tableLabelPremiumNetworkTip1": "Scenario 1: Applicable when the domain acceleration region is global (excluding Chinese mainland), and the user is in Chinese mainland.", "tableLabelPremiumNetworkTip2": "Scenario 2: Applicable when the domain acceleration region is global, and the origin server is outside Chinese mainland.", "tableLabelPremiumNetworkTip3": "Scenario 3: Applicable when the domain acceleration region is global or global (excluding Chinese mainland), the user is overseas, and the origin server is in Mainland China.", "tableLabelPremiumNetworkTip4": "Please confirm whether to disable Premium Network Service?", "tableLabelPremiumNetworkTip5": "Enabling Premium Network Service, please select a target scenario:", "tableLabelPremiumNetworkTip6": "Premium Network Service configuration will take effect within a short period of time.", "tableLabelPremiumNetworkTip7": "Please first disable and unsubscribe Premium Network Service and then try to change the acceleration region.", "scene": ["Scenario 1", "Scenario 2", "Scenario 3"], "domainCountLimitTip": {"tip1": "Insufficient CDN domain name quota. See ", "tip2": "Usage Restrictions."}}, "create": {"title": "Add Domain Name", "title2": "Add Domains by <PERSON><PERSON>", "titleTip": "The domain name you want to add is submitted via a ticket.", "reTitle": "Re-add Domain Name", "reTip": "Re-add Domain Name", "resubmit": "Resubmit", "basicInfo": "Basic Info", "domainName": "Domain Name", "tip1": "Enter a domain name that you want to add to eSurfing Cloud CDN. Wildcard domain names are supported, for example *.ctyun.cn.", "btn1": "Added Domains", "acceType": "Acceleration Type", "tip2": "Once the acceleration type is confirmed, it cannot be changed by yourself. Carefully select an acceleration type based on your business.", "domainType": "Domain Type", "acceRegion": "Accelerated Region", "serviceArea": "Service Area", "IPv6": "IPv6", "originSetting": "Origin Settings", "tip3-1": "submite a service ticket", "tips3": "If a client initiates a Range request, CDN cannot cache the files by default, the origin fetch traffic will be much greater than expected and incur additional fees. <br/> You are advised to <a target='_blank' href={documentOriginLink}  class='link'>request origin fetch of the entire file</a> or <a target='_blank' href={rangeOriginLink} class='link'>Range origin fetch</a> by <a target='_blank' href={orderLink} class='link'>submitting a service ticket</a>.", "originServer": "Origin Server", "originEncodeSelf": "Back-to-Origin Encryption Algorithm", "originEncodeSelfPlaceholder": "Default international encryption back-to-origin is not configured.", "proxyGmsslModeList": ["Chinese Encryption Back-to-Origin", "International Encryption Back-to-Origin", "Follow Client"], "number": "No.", "originType": "Origin Types", "level": "Level", "weight": "Weight", "originHost": "Specify an Origin Host", "addOrigin": "+Add Origin Server", "tip4": "IP addresses or domain names are supported. You can add up to 60 origin servers.", "tip5": "Up to 60 IPs, domain names, or media storage origins.", "tip5-1": "Supports IP or domain name, media storage origin, and ZOS origin, with up to 60 origins allowed.", "tip5-2": "Supports IP or domain name, or ZOS origin, with up to 60 origins allowed.", "originPolicy": "Origin Protocol Policy", "tip6": "If you select HTTP, port 80 is used to communicate with the origin server by default. If you select HTTPS, port 443 is used to communicate with the origin server by default. If you select Follow client, requests are sent to port 80 or 443 of your origin server over the protocol specified by the requests. Custom origin port numbers range from 1 to 65535.", "dynamicPolicy": "Dynamic Back to origin Policy", "originPort": "Origin Port", "originHost2": "Origin Host", "tip7": "If Optimum first is selected, requests are first sent to the origin server that responds in the shortest time regardless of the weight of the origin server. If By weight is selected, requests are sent to the origin servers based on their weights. If Keep logged in is selected, requests are sent to the origin servers based on the hash values of the client IP addresses.", "tip8": "The default origin host determines the site of the origin server to which back-to-origin requests are sent. The default value is the accelerated domain name. When you specify a custom origin host, make sure that the host is already configured on your origin server.", "https": "HTTPS", "httpsSwitch": "HTTPS", "httpsUpload": "Upload HTTPS Certificate", "certAlias": "Certificate Alias", "placeholder1": "Select a certificate", "tip9": "You can change the certificate (pay attention to the validity period of the certificate). If the target certificate is not found, click to upload it.", "tip9-1": "Click to Upload", "https2Switch": "HTTP2.0", "tls": "TLS Version", "placeholder2": "Select a TLS version. If this field is not configured, all versions are selected by default.", "cache": "<PERSON><PERSON>", "tip10": "You can customize the priority of each rule. A larger number indicates a higher priority.", "accessControl": "Access Control", "ip": "IP Trustlist/Blocklist", "tip11": "Use a trustlist or blocklist to identify and filter visitors. IPv6 addresses are supported.", "referer": "Referer-based Hotlink Protection", "referer2": "Allow Empty Referer", "referer3": "Allow Empty Protocol", "referer4": "Match all ports", "referer5": "Case-insensitive", "referer6": "Append or not", "tip12": "Use a trustlist or blocklist to identify and filter visitors.", "ua": "User-Agent Trustlist/Blocklist", "matchMethod": {"label": "Match Mode", "option1": "Regular", "option2": "Wildcard"}, "uri": "URL Trustlist/Blocklist", "tip13": "The origin server address cannot be the same as the accelerated domain name.", "tip14": "Each origin server address must be unique.", "tip15": "Invalid IP address or origin server address.", "tip16": "The specified IP address is a reserved one.", "tip17": "The entered weight must be an integer between 1 and 100.", "tip18": "You must specify one primary origin server.", "tip19": "The format of the specified origin host is invalid.", "tip20": "Specify origin host and default origin host cannot be configured at the same time. The specify origin host takes precedence.", "tip21": "Enter a certificate alias", "tip21-1": "Enter a certificate", "tip22": "Select an acceleration type", "tip23": "Select an accelerated region", "tip24": "Enter a domain type", "tip25": "Complete real-name verification first. Then, re-log in to the CDN console and try again.", "tip26": "Failed ​to add ​some domain names.", "tip27": "Successful.", "tip28": "The verification result is as follows:", "tip29": " succeeded.", "tip30": " failed. Check the following causes:", "tip31": "Enter a domain name or wait for the domain name verification to complete, and then try again.", "tip32": "The certificate is submitted.", "tip33": "The ticket is being created. View it later.", "tip34": "Are you sure you want to delete the ", "tip35": "origin server?", "title3": "Add Self-managed Certificate", "title4": "Update Self-managed Certificate", "tip36": "Upload a certificate. Pay attention to the validity period of the certificate. ", "certName": "Certificate Alias", "certs": "Certificate Public Key", "placeholder3": "Enter the public key of the certificate", "key": "Certificate Private Key", "placeholder4": "Enter the private key of the certificate", "xos0": "IP or Domain Name", "xos1": "XStor", "tip38": "The specified certificate alias is invalid. The certificate alias can only contain letters, digits, and the following special characters: _-*.()", "tip39": "Cannot exceed 255 characters in length.", "tip40": "The specified certificate alias already exists.", "tip41": "Cannot exceed 65,535 characters in length.", "primary": "Primary", "secondary": "Secondary", "tip42": "Currently, the media storage origin is only supported in the Chinese mainland. Using media storage as the origin can save costs on CDN back-to-origin traffic.", "tip43": "What is a media storage origin? ", "tip44-1": "Please note: ", "tip44": "1. The domain name of the media storage origin must end with {endPointStr}. If you are entering a domain name, make sure it is an activated bucket domain name.", "tip45": "2. When redirecting to the eSurfing Cloud Xstor origin, change the origin host to the bucket domain name. Please configure it through the \"Specify an Origin Host\" function.", "placeholder5": "Please enter or select the media storage origin.", "tip3-6": "If a client initiates a Range request, CDN cannot cache the files by default, the origin fetch traffic will be much greater than expected and incur additional fees. You are advised to request origin fetch of the entire file or Range origin fetch by <a target='_blank' href={orderLink} class='link'>submitting a service ticket</a>.", "tip46": "Redirecting to the domain list... Please wait.", "originServer-1": "Add Origin Server", "originServer-2": "Modify Origin Server", "tip47": "Integrated CDN does not support the media storage origin. Please remove related configuration from the origin servers.", "tip47-1": "Integrated CDN does not support the zos origin. Please remove related configuration from the origin servers.", "tip48": "Please select acceleration type.", "tip49": "Additional fees will be incurred after the configuration is enabled. Before purchasing a static HTTPS request package, you will be charged based on the {0} of static HTTPS requests by default. After purchasing a static HTTPS request package, the request quantity in the package will be deducted first. After exceeding the limit, you will be charged according to the request quantity.", "tip49-1": "standard pay-as-you-go rate", "tip50": "3. The media storage CDN supports on-demand and resource package billing for back-to-origin traffic. If the media storage you have activated is a subscription package ordered by time duration (with a traffic upper limit), the back-to-origin traffic will be charged as public network outflow traffic. Please confirm your billing method.", "tip51": "For details, refer to:", "tip52": "CDN Back-to-origin Traffic Billing Instructions.", "tip53": "When the switch is on, the input box is automatically cleared. Please fill in the content to be appended. The submitted will be incrementally distributed.", "routeSelectionMethod": "Traffic Steering", "tip54": "ICDN, ICDN - Upload Acceleration and ICDN - WebSocket Acceleration have different pricing. For details, see: ", "tip55": "ICDN billing", "tip56": "Upload Acceleration billing", "tip57": "and WebSocket Acceleration billing", "tip58": "ICDN, ICDN - Upload Acceleration and ICDN - WebSocket Acceleration have different pricing.", "tip59": ", ", "tip60": ".", "zos": {"tip1": "ZOS Origin", "tip2": "Please enter the ZOS origin.", "tip3": "The ZOS origin must end with {endPointStr}.", "tip4": "ZOS origin and media storage origin cannot be configured simultaneously.", "tip5": "Only one of media storage or ZOS origin can be configured.", "tip6": "Currently, the ZOS origin is only supported in the Chinese mainland. Using media storage as the origin can save costs on CDN back-to-origin traffic.", "tip7": " What is a ZOS origin?", "tip8": "1. The domain name of the ZOS origin must end with {endPointStr}. If you are entering a domain name, make sure it is an activated bucket domain name.", "tip9": "2. When redirecting to the eSurfing Cloud ZOS origin, change the origin host to the bucket domain name. Please configure it through the \"Specify an Origin Host\" function."}, "copy": {"tip1": "Reference Domain", "tip2": "Select a reference domain first.", "tip3": "Except for the back-to-origin, HTTPS configuration and UDFScript below, other acceleration configurations are set based on the reference domain. Select the same acceleration type and domain type for the ICDN reference domain. Once the reference domain is selected, you can view its configuration details. Only the general configurations are displayed here. Please contact the customer service for special configurations(Example: QUIC function).", "tip4": "Refer to Existing Domain Configuration", "tip5": "Acceleration Configuration", "tip6": "Custom Configuration", "tip7": "View Reference Domain Configuration Details", "tip8": "The domain name effectiveness configuration is subject to actual testing. If the configuration does not meet expectations, please contact customer service personnel for adjustment."}, "encryptionSuite": {"label1": "Encryption Suite", "label2": "Custom Encryption Suite", "option1": "All Encryption Suites", "option2": "Strong Encryption Suites", "option3": "Custom Encryption Suite", "placeholder1": "Default to All Encryption Suites", "tip1": "Please select the Custom Encryption Suite."}}, "detail": {"title": "View Domain Name", "btn": "Click to View", "tip1": "The {action} ticket you submitted is in progress.", "tip2": "Change of domain configuration is not supported when changing the accelerated region. You are advised to change the domain configuration before changing the accelerated region.", "tip3": "The configuration of this domain name is used by multiple products. This operation will modify the configuration for all of these products. Please click Confirm to continue.", "tip4": "Are you sure to modify the domain name?", "basicInfo": "Basic Info", "label1": "Domain Name", "label2": "Domain Status", "label3": "Acceleration Type", "label5": "Domain Type", "label6": "Accelerated Region", "label7": "Created At", "label8": "IPv6", "label9": "<PERSON><PERSON>", "label10": "Status Code Expiration", "label11": "HTTP Response Headers", "label12": "<PERSON><PERSON>", "label13": "<PERSON><PERSON> Rewrite", "label14": "Referer-based Hotlink Protection", "label15": "IP Trustlist/Blocklist", "label16": "User-Agent Trustlist/Blocklist", "label17": "URL Authentication", "label18": "Compression Settings", "label19": "HTTPS", "label20": "Upload HTTPS Certificate", "label21": "OCSP Stapling", "label22": "Force Redirect", "label23": "Origin Servers", "label24": "Origin protocol policy", "label25": "301/302 Redirect", "label26": "HTTP Request Headers", "label27": "Back-to-origin URI Rewrite", "label28": "Private Bucket Access", "label29": "Origin Parameter Filtering", "label30": "Origin Parameter Rewrite", "label31": "Script Name", "tip5": "Not updated", "tip6": "Are you sure you want to change the acceleration type to CDN? Changing the acceleration type may affect billing. Please operate with caution.", "tip7": "Ticket for changing the acceleration type submitted.", "tip8": "You have not activated the CDN service. Please go to <a target='_blank' style='color: #0004ed;' href='{link}'>CDN</a> to activate it first.", "tip9": "You can configure cache expiration rules for custom resources by specifying paths or filename extensions.", "tip10": "For global configurations, Origin first applies by default.", "tip11": "By default, caching ignore query strings is enabled, but if you need to cache with query strings, please choose to disable the feature.", "tip12": "You can set the weight to customize the priority of each rule. A larger weight indicates a higher priority.", "tip13": "You cannot modify the custom cache rule", "label32": "<PERSON><PERSON>", "label33": "TTL", "label34": "Ignore Query Strings When Caching", "label35": "Custom cache", "label36": "Do not cache", "label37": "Origin first", "label38": "Force cache", "placeholder2": "second", "label39": "URL Cache", "placeholder1": "Enter file types separated by commas", "label40": "Other Files", "tip14": "Set TTLs for error status codes, supporting ", "label41": "Status Code", "label42": "Status Code Cache", "tip15": "Manage headers in HTTP responses returned from an origin server. You can add, modify, and delete HTTP response headers so that the HTTP response headers bring specific parameters to clients.", "label44": "Value", "label43": "Parameter", "label45": "HTTP Response Headers", "tip16": "The parameter name can only contain uppercase letters, lowercase letters, digits, underscores (_), and hyphens (-).", "placeholder4": "Enter a response header parameter", "placeholder5": "Enter a value", "tip17": "Chinese characters are not supported.", "tip18": "If you leave this field empty, the corresponding response header is deleted.", "label46": "Ignore Parameters", "label47": "Specify Parameters", "label48": "Priority", "label49": "PATH to Be Rewritten", "label50": "Target PATH", "label51": "Not Ignore", "label53": "Retain Specified Parameters", "label52": "Ignore All", "label54": "Ignore Specified Parameters", "tip19": "Example of retaining specific parameters: a=$arg_a&b=$arg_b, where $arg_a represents the value of parameter a after the question mark.", "tip20": "Example of ignoring a specified parameter: a,b. Separate multiple parameters with commas (,).", "tip21": "Currently, this feature cannot be configured on your own. If you need to configure this feature, <a target='_blank' href='{orderLink}'>submit a ticket</a> to the eSurfing Cloud technical support team. This feature can only be manually configured by the technical support team.", "tip22": "You can rewrite the URIs by creating one or more rewrite rules. Regular expressions are supported. The system runs the listed rewrite rules in top-to-bottom order to match requests.", "tip23": "Learn More", "tip24": "The URI starts with a forward slash (/) and does not contain http:// or the domain name, ? or parameters. Regular expressions are supported, e.g. ^/test$.", "tip25": "The URI starts with a forward slash (/) and does not contain http:// or the domain name, ? or parameters. Regular expressions are supported. $1 and $2 are commonly used to capture the strings in parentheses (()) in the PATH to be overwritten.", "cacheModeMap": ["Filename extension", "Directory", "Homepage", "All files", "Absolute path file", "Regular"], "ttlTip": ["Enter a TTL", "Enter a valid TTL.", "Enter a number", "If the unit is day, the maximum TTL is 1095.", "If the unit is hour, the maximum TTL is 26280", "If the unit is minute, the maximum TTL is 1576800", "If the unit is second, the maximum TTL is 94608000"], "placeholder6": "Enter the PATH to modify", "placeholder7": "Enter the target PATH", "placeholder8": "Enter the Status Code", "tip26": "Do not enter repeated status codes. Check ", "tip27": "Please enter a valid 3xx or 4xx or 5xx status code.", "tip28": "The parameter already exists.", "tip29": "Chinese characters are not supported.", "placeholder9": "Please enter filename extensions", "placeholder9-1": "Please enter filename extensions", "placeholder10": "Invalid filename extension format. Please verify and enter again.", "placeholder11": "Enter directories", "placeholder11-1": "Enter directories", "placeholder12": "Invalid directory format. Re-enter directories.", "placeholder13": "Please enter an absolute path file", "placeholder13-1": "Please enter an absolute path file", "placeholder14": "Invalid format for the absolute path file. Re-enter an absolute path file.", "placeholder15": "Select the parameters you want to ignore", "placeholder16": "Enter the specified parameters", "placeholder17": "Chinese characters are not supported.", "placeholder18": "Enter the content to cache", "tab1": "<PERSON><PERSON>", "placeholder19": "Specify a valid TTL.", "placeholder20": "Enter a weight", "placeholder21": "Enter an integer in the range of 1 to 100", "placeholder22": "Enter a priority", "tip30": "Invalid file type format. Please verify before clicking OK.", "tip31": "Duplicate", "tip31-1": "is not allowed. Please check ", "tip32": "Are you sure to delete the selected cache rule?", "tip33": "Are you sure to delete the selected status code expiration rule?", "tip34": "Are you sure  to delete this configuration item?", "trustlist": "Trustlist", "blocklist": "Blocklist", "label55": "Authentication Type", "label56": "Encryption Key", "label57": "Encryption Element Delimiter", "label58": "Timestamp Encryption Parameter", "tip35": "After URL authentication is enabled, CDN verifies encrypted strings and timestamps, thus protecting website resources.", "tip36": "Currently, this feature cannot be configured on your own. If you need to configure this feature, <a target='_blank' href='{orderLink}'>submit a ticket</a> to the eSurfing Cloud technical support team. This feature can only be manually configured by the technical support team.", "authType": ["Authentication A", "Authentication B", "Authentication C"], "placeholder23": "Separate multiple keys with commas (,).", "label59": "Encryption Parameter", "label60": "MD5 Encryption Parameter", "label61": "TTL of Authentication URL", "placeholder24": "Enter encryption parameter", "placeholder25": "Enter timestamp encryption parameter", "tip37": "Up to {maxNum} referers are supported. Separate multiple referers with line breaks. A referer can contain wildcards, IP addresses, and port numbers. Example: \n*.test.com\ntest.com:80\n127.0.0.1:80\nlocalhost\n*.test.com(:[0-9]+)? (matches all ports)", "tip38": "Up to {maxNum} referers are supported. Separate multiple referers with line breaks. A referer can contain wildcards, IP addresses, and port numbers. Example：\n*.test.com\ntest.com:80\n127.0.0.1:80\nlocalhost", "tip39": "Up to ${maxNum} referers are supported. Separate multiple referers with line breaks. A referer can contain CIDR block addition. Example: 127.0.0.1/24", "tip40": "Separate multiple referers with line breaks. A referer can contain CIDR block addition. Example: 127.0.0.1/24", "tip41": "Up to 400 entries, separated by newlines. Supports regex (no * prefix) or wildcards. Configure per match mode, e.g.:\nWildcard:\nwget means exact match\n*wget* means fuzzy match\nRegular expression:\nwget means fuzzy match\n^wget$ means exact match", "tip41-1": "Up to 400 URIs separated by line breaks, starting with \"/\", containing \"?\" and parameters, and excluding \"http://\" and domain names. Regular expressions are supported, for example: \nMatch level 1 directory: ^/aa/\nStrict match without parameters: ^/aa/bbb/a\\.txt$\nMatch with parameters: ^/aa/bbb/a\\.txt(\\?.*)?$", "tip42": "Select a type", "tip43": "The number of addresses that you entered exceeds {maxNum}.", "tip44": "Duplicate domain names are detected.", "tip45": "Enter an IP address", "tip46": "The number of IP addresses that you entered exceeds {maxNum}.", "tip47": "Invalid IP address format. Enter the IP address again.", "tip48": "Duplicate IP addresses are detected.", "tip49": "Enter User-Agent addresses", "tip49-1": "Enter URL addresses", "tip50": "The number of entries you entered exceeds {maxNum}.", "tip51": "Enter the encryption key", "tip52": "Up to 3 keys separated by commas are allowed", "tip53": "Separate multiple keys with English commas. Each key can contain 1-128 characters, and characters including $;, not allowed.", "tip54": "Enter a maximum of 8 integers", "label62": "File Compression", "label68": "Compression Method", "label63": "File Types", "label64": "Minimum File Size", "label65": "Compression Method", "label66": "File Types", "label67": "Minimum File Size", "placeholder26": "Separate multiple file types with commas (,). If you want to specify all file types, enter *.", "tip55": "Supported file types: text/xml,text/plain,text/css,application/javascript,application/x-javascript,application/rss+xml,text/javascript,image/tiff,image/svg+xml,application/json,application/xml", "tip56": "The maximum file size is 1-1023 B, 1-1023 K, or 1-102399 M.", "tip57": "Enter the minimum file size that you can compress", "tip58": "Enter a valid minimum file size.", "tip59": "Select a compression method", "tip60": "Enter file types", "label69": "OCSP Stapling", "label70": "Redirect Type", "label71": "Redirect Method", "jumpType": ["302 Redirect", "301 Redirect"], "tip61": "Select the redirect type", "tip62": "Select the redirect method", "tip63": "The option is invalid.", "originType": ["Optimum first", "By weight", "Keep logged in"], "label72": "301/302 Redirect", "label73": "Origin Host Valid", "label74": "Maximum Redirects", "tip64": "The default value is 1. Valid values: 1 to 5.", "tip65": "You can rewrite the URIs by creating one or more rewrite rules. Regular expressions are supported. The system runs the listed rewrite rules in top-to-bottom order to match requests.", "tip66": "The URI starts with a forward slash (/) and does not contain http:// or the domain name. Regular expressions are supported. Example: ^/test$", "tip68": "This feature allows CDN to pull from a private media storage bucket using a sub-account’s AK/SK, granting CDN read-only access to the bucket.This is effective only when a media storage origin is configured.", "tip68-1": "Configure CDN to retrieve content from a private ZOS Bucket, using the sub-account’s AK/SK, and grant the CDN read-only access to your ZOS Bucket. This is only effective when a ZOS origin is configured.", "tip69": "Origin parameter configuration rules allow you to add, delete, back up, and modify the origin parameters.", "label75": "Ignore Origin Parameters", "label76": "Rewrite the parameter mode", "label78": "Parameter name", "label79": "parameter value", "label80": "Append", "label81": "Overwrite", "label90": "Cache Time Rule", "label91": "FLV Scrubbing", "label92": "Scrubbing Mode", "label93": "Starting Parameters", "label94": "End Parameters", "label95": "MP4 Scrubbing", "label96": "Enabled", "label97": "Disabled", "label98": "Web Acceleration", "label99": "Download", "label100": "VOD", "label101": "Livestreaming", "label102": "ICDN", "label103": "SCDN", "label104": "CDN", "label105": "App Acceleration", "label106": "Web Application Firewall (Edge Cloud)", "label107": "Anti-DDoS (Edge Cloud)", "label108": "smart dns", "label109": "Website Security Monitoring", "label110": "Download (Idle Time)", "label111": "RTC", "label112": "分布式安全云平台en", "label113": "容器安全平台en", "label114": "Crawler Management", "label115": "GTM", "label116": "EdgeTrans", "label117": "ZeroTrust", "label118": "Key Service Assurance", "label119": "AccessOne", "label120": "Upload Acceleration", "label121": "Keep sequence", "label122": "Encode parameter", "label123": "Origin first. Do not cache headers.", "label124": "IP Blocklist/Trustlist Set", "label125": "IP Set Name", "tip70": "If this parameter is left empty, the parameter is deleted.", "tip71": "Example of retaining specific parameters: Parameter name: a, parameter value: $arg_a, where $arg_a represents the value of parameter a after the question mark. Parameter names with hyphens are not supported, such as: a-b. To configure, please submit a ticket.", "tip72": "Select a dynamic back-to-origin policy.", "tip73": "Enter the origin server", "tip74": "Select a level", "tip75": "Select an origin protocol policy.", "tip76": "Enter a request header parameter", "tip77": "The host request headers cannot be modified in this module. If you want to modify such headers, go to Back-to-origin > Origin Host.", "tip78": "Empty strings are not supported.", "tip79": "Enter the PATH to be rewritten", "tip79-1": "Enter the PATH to be rewritten", "tip80": "Enter the target PATH", "tip80-1": "Please enter the new PATH", "tip81": "Enter the AccessKey ID", "tip82": "Enter the secret access key", "tip83": "Enter the parameter name", "tip84": "The parameter name can only contain uppercase letters, lowercase letters, digits", "tip85": "Up to {num} characters in length.", "tip86": "Up to {num} characters in length.", "label82": "Configuring Back-to-origin Parameter Rewrite", "tip87": "Are you sure to delete the selected configuration?", "tip88": "Keep at least one line of configuration.", "tip89": "Invalid IP address or origin server address.", "tip90": "Enter an HTTP port.", "tip91": "Enter a valid port number in the range of 1 to 65535. Port 443 is not supported.", "tip92": "Enter an HTTPS port.", "tip93": "Enter a valid port number in the range of 1 to 65535.", "tip94": "Valid values: 1 to 5", "tip95": "Enter a number", "tip96": "Up to 60 entries can be added.", "label83": "No business scripts bound", "label84": "External Link Conversion Layer", "label85": "Effective Scope", "label86": "IPv6 Requests", "label87": "IPv4&IPv6 Requests", "label88": "External Link Conversion Blocklist", "label89": "External Click Link Conversion", "tip97": "Manage headers in HTTP requests by adding, modifying, and deleting HTTP headers that bring specific parameters to the origin server.", "tip98": "Chinese characters are not supported.", "tip99": "If you want to add or modify the corresponding request header, enter a new value.", "tip100": "If you want to delete the corresponding request header, leave the Value field empty.", "label77": "Parameter name: parameter value", "tab2": "Basics", "tab3": "Back-to-origin", "tab4": "Configurations", "tab5": "HTTPS", "tab6": "<PERSON><PERSON>", "tab6-1": "Cache Expiration", "tab7": "Cache Expiration", "tab8": "Status Code Expiration", "tab9": "HTTP Response Headers", "tab10": "File Processing", "tab11": "Business Scripts", "tip101": "Input error in the table, please recheck it", "tip102": "The domain name of the media storage origin is incorrect. Please enter again.", "tip103": "Only one domain name of the media storage origin can be configured.", "placeholder27": "Enter the specified origin HOST", "placeholder28": "Empty string by default", "placeholder29": "Please enter the expiration time", "placeholder30": "Value range: 0 to ********", "placeholder32": "Enter only integer from 1 to 100.", "placeholder33": "Please confirm whether to submit and save the allocation configuration, which may take a few minutes to take effect. The final configuration will be displayed, and it cannot be modified again. Please proceed with caution.", "placeholder34": "Implementation principle: EdgeTrans secure acceleration accesses the external link origin site through a proxy, converts the external link address at the edge cloud node, and provides IPv6 acceleration to the external link to ensure a smooth usage and display experience", "placeholder35": "By clicking on Confirm, you indicate that you have understood the eSurfing Cloud external link conversion service and its related principles. You are aware of the risks associated with implementing the service, agree to accept the eSurfing Cloud external link conversion service, and agree to assume all responsibilities arising from it on your own", "placeholder36": "To implement IPv6 external link conversion, please first enable IPv6 resolution capability in domain name management.", "placeholder37": "WebSocket protocol will support the server actively pushing data to the client. After enabled, the default connection timeout is 5 seconds, and the request timeout is 15 seconds.", "placeholder38": "Learn more", "placeholder39": "When not configured, the websocket back-to-origin connection timeout is the same as the back-to-origin connection timeout. If the latter is not configured, the defaults timeout is 5 seconds.", "placeholder40": "Please enter an integer between 1 and 300.", "placeholder41": "When the back-to-origin timeout switch is turned on, enter the back-to-origin connection timeout or back-to-origin request timeout.", "placeholder42": "Please enter an integer between 0 and 300.", "placeholder43": "Please enter the protocol and port precisely.", "placeholder44": "For packages supporting self-service configuration of server ports, please refer to the {0}. When the acceleration region is set to \"Global\" or \"Global (excluding Chinese Mainland),\" self-service configuration of server ports is not allowed. If you need to enable this feature, please {1} or contact customer service.", "placeholder44-0": "For special ports, please submit a ticket or contact technical support.", "placeholder44-1": "version comparison", "placeholder45": "Ports cannot be duplicated.", "placeholder46": "HTTP port and HTTPS port cannot be duplicated", "placeholder47": "Ignoring specified parameter example: \"a, b\", where multiple parameters are separated by commas.", "placeholder48": "An absolute path file cannot contain ‘?’", "placeholder49": "An absolute path file should start with /", "placeholder50": "Please enter the correct suffix and separate them with commas.", "placeholder51": "Please enter the correct directory, starting with / and separated by comma.", "placeholder52": "Please enter the homepage.", "placeholder53": "Please enter all files.", "placeholder54": "Please enter.", "placeholder55": "Day", "placeholder56": "Hour", "placeholder57": "Minute", "placeholder58": "Please select a caching rule.", "placeholder59": "The expiration time can only be an integer.", "placeholder60": "Unit in days, the maximum expiration time is 365", "placeholder61": "Unit in hours, the maximum expiration time is 8760", "placeholder62": "Unit in minutes, the maximum expiration time is 525600", "placeholder63": "Unit in seconds, the maximum expiration time is ********", "placeholder64": "Please choose to go to the question mark cache", "placeholder65": "The weight can only be an integer.", "placeholder66": "The maximum weight value is 100", "placeholder67": "The minimum weight value is 1", "placeholder70": "<PERSON><PERSON><PERSON> by time", "placeholder71": "Scrub by byte", "placeholder72": "Please select the scrub mode.", "placeholder73": "Please enter a directory.", "placeholder74": "Please enter an absolute path file.", "placeholder75": "The priority can only be an integer.", "placeholder76": "The maximum priority value is 100.", "placeholder77": "The minimum priority value is 1.", "placeholder78": "At least one piece of data is required.", "placeholder79": "Upload acceleration and websocket cannot be enabled at the same time.", "placeholder80": "Please enter the origin server address", "placeholder81": "Separate multiple ports with commas", "placeholder82": "The service port and protocol muse match precisely. Please enter with carefully.", "placeholder83": "Separate multiple ports with commas, each ranging from 1 to 65535", "placeholder84": "Separate multiple values with commas.", "tip63-1": "The options contain invalid values.", "placeholder3": "Enter one or more status codes, separated with commas (,)", "tip104": "Currently, only ICDN supports the global accelerated region. Please change the accelerated region first.", "tip105": "3xx: 301, 302, etc (304 not supported)", "tip106": "4xx: 400, 401, 403, 404, 405, 407, 414, etc", "tip107": "5xx: 500, 501, 502, 503, 504, 509, 514, etc", "tip108": "When static acceleration is disabled, all files are not cached by default. Static cache acceleration cannot be used.", "routeType": ["Quick Traffic Steering", "Stable Traffic Steering", "App Layer Traffic Steering"], "tip109": "(Trial) Default upload size limit: 300M upon activation. It is recommended to add dynamic acceleration for better acceleration results.", "tip110": "When Keep sequence and Encode parameter are both enabled, paramters will be decoded and then encoded.", "tip111": "Please enter the backup source address provided by the eSurfing Cloud AOne.", "limitSpeed": {"tip1": "Single Request Speed Limit", "tip2": "Speed Limit", "tip3": "Please enter a speed limit value.", "tip4": "Maximum speed limit: 100MB/s", "tip5": "Minimum speed limit: 1B/s", "tip6": "Single Request Speed Limit"}, "ja3_fingerprint": "JA3 fingerprint", "ja3_fingerprint_log": "JA3 fingerprint log", "ja3_fingerprint_tip": "Enable JA3 fingerprint logging. Access control supports \"JA3 fingerprint\" matching, and rate limiting supports \"JA3 fingerprint\" matching and granularity settings.", "ja3_fingerprint_tip2": "Enable JA3 fingerprinting to ensure proper function. Configure in ", "ja3_fingerprint_tip2-1": "Security Ability > Access Control/Rate Limit", "ja3_fingerprint_tip2-2": ".", "rewriteMode": "Rewriting Mode", "rewriteModeTip": "Decoding (default): Rewrite after decoding the back-to-origin URI; Encoding (when selected): rewrite the original encoding of the back-to-origin URI.", "rewriteModeList": ["Decode", "Code", "Default Code"]}, "content": "Content", "add2": "Add", "add3": "Creating", "close": "close", "unknown": "Unknown", "areaScope": ["Chinese mainland", "Global (excluding Chinese mainland)", "Global"], "batch": {"title": "Batch Modify", "next": "Next", "title1": "Confirm Batch Domain Name Modification", "title2": "Modify", "title3": "Finish", "tip1": "Are you sure to modify the configurations of the selected domain names?", "tip2": "Failed to modify some domain names", "emptyReferer": "Empty Referer", "tip3": "Invalid IP address format. Please enter the domain address again.", "tip4": "IP addresses or domain names are supported. You can add up to 60 origin servers. The added content of the origin server will overwrite the existing domain configuration.", "label1": "HTTP Headers", "以下根据勾选的功能进行覆盖下发": "Overwritten based on selection", "请至少修改一项配置后继续操作": "Please modify at least one configuration to continue."}, "title": "Domain Management", "domainList": "Domain List", "domainEdit": "Edit Domain Name", "domainCreate": "Added Domains", "domainBatch": "Batch Modify", "domainView": "View Domain Name", "dialog": {"label1": "Domain:", "placeholder1": "Select a tag group", "tip1": "Operation successful."}, "comfirm": " Confirm ", "comfirm2": "Confirm Deletion", "comfirm3": "Confirm to Enable", "editTip": {"tip1": "You can change the acceleration type by", "tip2": " submitting a ticket", "tip3": "."}, "editPage": {"btn1": "Upgrade to Basic Edition", "btn2": "Edit Configuration", "btn3": "Cancel", "btn4": "Save", "btn5": "Discard changes", "tip1": "The domain name is currently configured/disabled and does not support capability configuration. Domain name status must be enabled before it can be edited.", "tip2": "Default timeout for back-to-origin connection is 5s, and default timeout for the back-to-origin request is 15s.", "tip3": "Please first request the protocol to enable HTTPS before proceeding with HTTPS related configurations.", "tip4": "To set Include Subdomains to Yes, confirm that all subdomains have HTTPS enabled first. Otherwise, the subdomain will automatically jump to HTTPS and cause access failure.", "tip5": "Configure the forced redirection function to redirect the original request method from the client to the edge node to HTTP or HTTPS requests", "tip6": "Add, modify, or delete the HTTP header information returned to the client", "tip7": "Add, modify, or delete the HTTP header information carried during the back-to-origin process", "tip8": "Compress Mode", "tip9": "File Type", "tip10": "After enabling the URL authentication function, the encrypted string and timestamp will be verified to effectively protect site resources. Click to ", "tip11": "EdgeTrans accesses the external link origin through proxies, rewrites the external link address at the edge cloud node, and provides IPv6 acceleration to the external link, ensuring a smooth usage and display experience.", "tip12": "The websocket protocol defined in HTML5 is a new TCP-based network protocol, which can effectively save server resources and bandwidth. AccessOne is based on abundant edge nodes, and combines dynamic detection and intelligent scheduling to effectively minimize real-time communication latency.", "tip13": "The current domain name has unsaved configurations. Are you sure you want to discard the changes?", "tip14": "The current domain name is being added and the page displays initial content. Please wait for 5-10 minutes", "tip15": "Failed to obtain the switch in soc-cbs-center. Please refresh the page and try again.", "tip16": "Domain name configuration in progress/disabled, editing not supported.", "tip17": "Modification not allowed due to special configuration. Please submit a ", "tip18": " or contact the customer service.", "tip19-prev": "Currently, the media storage origin is only supported in the Chinese mainland. Using media storage as the origin can save costs on CDN back-to-origin traffic.", "tip19": "Using {0} or {1} as the origin can save more on CDN back-to-origin traffic costs.", "tip19-1": "media storage", "tip19-2": "ZOS", "tip19-3": "1. Media storage origins must end with ctyunxs.cn or xstore.ctyun.cn, and ZOS origins must end with {endPointStr2}. You can only configure one type of origin, either media storage or ZOS.", "tip19-4": "2. When returning to the media storage origin or ZOS origin, you must also modify the back-to-origin host to the Bucket domain by specifying the origin host.", "tip19-5": "3. You cannot configure both media storage origin and ZOS origin for a single domain name but only select either of the two.", "tip20": "Are you sure to delete this configuration item?", "tip21": "Non-standard service ports are currently not supported. To enable, please submit a ", "tip22": "customer service ticket", "placeholder1": "Support regular expressions, URI starting with/, excluding http:// and domain name.", "placeholder2": "URLs starting with /, excluding the http://header and domain name.", "placeholder3": "Only supports uppercase and lowercase letters, numbers, underscores, and hyphens.", "placeholder4": "To delete the response header, leave it blank. Chinese not supported.", "placeholder5": "To delete the back-to-origin request header, leave it blank. Chinese not supported.", "placeholder6": "Supported file types: text/xml,text/plain,text/css,application/javascript,application/x-javascript,application/rss+xml,text/javascript,image/tiff,image/svg+xml,application/json,application/xml", "placeholder7": "Separate multiple types with English commas. If all types are matched, enter *", "placeholder8": "Please select", "placeholder9": "Please select the request protocol", "placeholder10": "URI starting with /, including ? and parameters, excluding http:// and domain name, and supporting regular expressions such as (.+)", "placeholder11": "Complete request, including protocol, domain name, ?, and parameters. Usually use $1, $2 to capture strings within parentheses in the pattern, for example, in $scheme://$host$1, $scheme represents the client request protocol, such as http or https. $host represents the current accelerated domain name. ", "placeholder13": "No by default", "placeholder14": "All files are matched by default when the type is empty.", "label1": "Basic Info", "label2": "Product Type", "label3": "Origin Type", "label4": "Origin Port", "label5": "Back-to-origin Timeout Settings", "label6": "Connection Timeout", "label7": "Request Timeout", "label8": "Origin URI Rewrite", "label9": "New PATH", "label10": "Add Rule", "label11": "Add Back-to-origin Parameter Rule", "label12": "Request Protocol", "label13": "Include Subdomains", "label14": "Modify Header", "label15": "Header Value", "label16": "IPv6 Transition", "label17": "Allow Empty Protocol", "label18": "Effective for 301/302 Redirect", "label19": "Certificate", "label20": "Add Origin", "label21": "Website Homepage IPv6 Prompt", "label22": "IPv6 Prompt Content", "label23": "IPv6 Prompt Display Duration", "label24": "Yes", "label25": "No", "label26": "Service Port", "label27": "Static Configuration", "label28": "Dynamic Configuration", "label29": "Upload Configuration", "label30": "HTML Optimization", "label31": "Video Scrubbing", "label32": "Follow Request Port", "label33": "Cross-domain Authentication", "label34": "Advanced", "label35": "Redirect Status Code", "label36": "Access URL Redirection", "label37": "Scale", "label38": "Coefficient", "label39": "Error Page Redirection", "label40": "Error Status Code", "label41": "Redirect Page", "radio1": ["HTTP", "HTTPS", "Follow client"], "ipsetTip": "Takes priority over IP Blocklist/Trustlist", "certTip": "The certificate is sourced from eSurfing Cloud certificate management platform. If no certificate is found.", "ipv6Tip": "This website is IPv6 compatible", "domainFailTip": "Domain name information acquisition failed. Please refresh the page.", "tooltip1": "The back-to-origin HTTP request header does not support HOST request header modification. To modify the back-to-origin HOST header, go to Back-to-origin > Origin Host.", "websocketTip": "The websocket protocol defined in HTML5 is a new network protocol based on TCP, which can effectively save server resources and bandwidth. Based on rich edge nodes, EdgeTrans is combined with dynamic detection and intelligent scheduling to effectively improve real-time latency in communications scenarios.", "tooltip2": "When using IPv6, enable IPv6 prompt to display \"This website is IPv6 compatible.\" in the bottom right corner of the website homepage. You can disable IPv6 prompt to hide the message.", "tooltip3": "The IPv6 prompt content supports up to 50 characters", "tooltip4": "Enabling IPv6 external link rewriting will convert external link addresses at edge nodes, providing IPv6 external link acceleration capability and improving IPv6 support to ensure a smooth usage and display experience.", "tooltip5": "The number of external link conversion layer is 0 by default, which represents unlimited conversion layers.", "tooltip6": "Supports external link conversion of IPv6 requests only, or simultaneous implementation of IPv4 and IPv6 requests.", "tooltip7": "You can configure addresses that do not require external link conversions. Please enter the domain name, uri, and url. Separate multiple blocklists with commas, for example: www.abc.com,/two_level.com", "tooltip8": "Please enter the domain name, uri, and url. Separate multiple blocklists with commas.", "tooltip9": "Supports converting external click links", "tooltip10": "The current domain name has unsaved configuration. Do you want to save the configuration before switching?", "tooltip11": "en", "tooltip12": "en", "tooltip13": "en", "tooltip14": "en", "ruleTip1": "Please enter the header value.", "ruleTip2": "The length cannot exceed 300 characters.", "ruleTip3": "The parameter only supports uppercase and lowercase letters, numbers, underscores, and hyphens.", "ruleTip4": "Please enter the IPv6 prompt content", "ruleTip5": "The IPv6 icon content supports up to 50 characters.", "ruleTip6": "Please enter the duration for displaying the IPv6 prompt", "ruleTip7": "Only positive integers are allowed.", "ruleTip8": "Value range: 1 to 2147483647", "ruleTip9": "Please select the number of external link conversion layers.", "ruleTip10": "Please select the effective scope of the conversion.", "ruleTip11": "Supported status codes: 400, 401, 403, 404, 405, 407, 414, 500, 501, 502, 503, 504, 509, 514", "ruleTip12": "The expiration time can only be an integer.", "ruleTip13": "Unit in days, the maximum expiration time is 365", "ruleTip14": "Unit in hours, the maximum expiration time is 8760", "ruleTip15": "Unit in minutes, the maximum expiration time is 525600", "ruleTip16": "Unit in seconds, the maximum expiration time is ********", "ruleIpset": "IP set name cannot be empty", "dynamicTip": "Provides dynamic request acceleration services. AceesOne improves the access speed of dynamic requests by detecting global node latency in real-time, using intelligent detection routing, self-developed protocols, and kernel optimization technologies.", "tooltip15": "After enabling dynamic acceleration, the number of dynamic requests between client users and edge nodes will be charged in addition to the subscription package.", "tooltip16": "Billing instructions", "tip23": "Collapse", "tip24": "Expand", "tip25": "Submission failed. Please verify.", "tip26": "When enabled, the request port will be used for origin fetch.", "tip27": "You cannot change the related source station configuration for this accelerated domain name.", "tip28": "You cannot add or change custom HTTP response header configurations for content-type and content-disposition for this accelerated domain name.", "tip29": "You cannot add accelerated domain names with the {xosDefaultAccelerateSuffix} suffix.", "tip30": "Allowed values: 300–599 (excluding 499), separated by commas.", "tip31": "At least 16 characters and must start with http:// or https://.", "tip32": "For the same error status code, only the configuration with the higher priority takes effect.", "tip33": "ICDN - Upload Acceleration and ICDN have different pricing. For details, see: ", "tip34": "Upload Acceleration billing", "tip35": ". Please confirm your modification.", "tip36": "ICDN - Upload Acceleration and ICDN have different pricing. Please confirm your modification.", "tip37": "ICDN - WebSocket Acceleration and ICDN have different pricing. For details, see: ", "tip38": "WebSocket Acceleration billing", "tip39": "ICDN - WebSocket Acceleration and ICDN have different pricing. Please confirm your modification.", "tip40": "Please enter the error status code.", "tip41": "Please enter a redirect page.", "tip42": "Please enter the redirect status code.", "tip43": "The format of the entered redirect page is incorrect. Please enter again.", "tip44": "Cannot end with ','.", "tip45": "ICDN - Upload Acceleration and ICDN - WebSocket Acceleration have different pricing. For details, see:", "tip46": "ICDN - Upload Acceleration and ICDN - WebSocket Acceleration have different pricing. Please confirm your modification.", "tip47": "ICDN - WebSocket Acceleration and ICDN - Upload Acceleration have different pricing. For details, see:", "tip48": "ICDN - WebSocket Acceleration and ICDN - Upload Acceleration have different pricing. Please confirm your modification.", "tip49": "Certificate expires or not exists. Please select one.", "sni": {"tip1": "Back-to-origin SNI", "tip2": "Exact domain name only, wildcard domains not supported", "tip3": "Please enter the SNI.", "tip4": "Only applies to HTTPS origin protocol", "tip5": "Incorrect domain format."}, "split": {"tip1": "Range GETs", "tip2": "Disable", "tip3": "Enable", "tip4": "Adaptive", "tip5": "Range GETs (range-based origin fetch) refers to the process where a CDN node, upon receiving a user request, includes a Range request header when fetching data from the origin server. The origin server then returns the corresponding range of content to the CDN based on the Range request.", "tip6": " Click here to {0}.", "tip6-1": "learn more", "tip7": "1. Enabled: Regardless of whether the client initiates a Range request, if the CDN node does not have cached content, it fetches data from the origin in configured shard sizes.", "tip8": "2. Adaptive: If the client includes a Range request header, the CDN fetches data using shard-based origin fetch. If the client does not include a Range request header, the CDN fetches the entire file from the origin."}, "specialPortTip2": {"tip1": "The resources used by the current domain have specific constraints and do not support self-service configuration of non-standard server ports. If configuration is required, please {0} or contact customer service.", "tip2": "submit a service ticket"}, "originPortTip": {"tip1": "Follow Request Port takes effect first.", "tip2": "Enabled when Follow Request Port takes effect first. The request port will be used for origin fetch."}}, "changeAcceArea": {"title": "Change Acceleration Region", "ctcloud": {"tip1": "1. Billing standards vary based on accelerated regions. If you select \"Chinese mainland\" or \"Global\":", "tip1-1": "1) Please complete real-name authentication for your account by refering to the <a target='_blank' href={link} class='link'>guide</a>, and log in to your account again.", "tip1-2": "2) Please complete an ICP filing of the Chinese mainland for the accelerated domain.", "tip2": "2. Modifying the accelerated region may cause the back-to-origin traffic to increase in the short term due to changes in service nodes, and the hit rate will decrease. Please pay attention to the operation of the origin server.", "tip3": "3. Modifying the accelerated region may cause a change in the back-to-origin IP. If your origin server has a trustlist on the back-to-origin IP, please submit the email addresses for receiving the back-to-origin trustlist through the <a target='_blank' href={link} class='link'>[Customer Service Ticket]</a>. After receiving the back-to-origin trustlist sent by the eSurfing Cloud CDN team, complete the trustlist configuration on the origin, and then change the accelerated region in the console."}, "ctyun": {"tip1": "1. Billing standards vary based on accelerated regions.If the Chinese mainland is included, complete an ICP filing for the domain name in the Chinese mainland and then complete a Public Security Bureau (PSB) filing.", "tip2": "2. Modifying the accelerated region may cause the back-to-origin traffic to increase in the short term due to changes in service nodes, and the hit rate will decrease. Please pay attention to the operation of the origin server.", "tip3": "3. Modifying the accelerated region may cause a change in the back-to-origin IP. If your origin server has a trustlist on the back-to-origin IP, please submit the email addresses for receiving the back-to-origin trustlist through the <a target='_blank' href={link} class='link'>[Customer Service Ticket]</a>. After receiving the back-to-origin trustlist sent by the eSurfing Cloud CDN team, complete the trustlist configuration on the origin, and then change the accelerated region in the console."}, "tip4": "The accelerated region modification ticket is submitted.", "tip5": "Change Acceleration Region", "tip6": "Click to change the acceleration type as required"}, "ipv6": {"tip1": "IPv6 DNS will be disabled. Are you sure to continue?", "tip2": "Please confirm whether IPv6 DNS is required.", "tip3": "The IPv6 DNS configuration will take effect within a short period of time."}, "htmlForbid": {"forbid1": "HTML Prohibited Operation", "forbid2": "Prohibit Copying", "forbid3": "Prohibit Right Clicking", "forbid4": "Please enter the suffix.", "forbid5": "Configure the type and content at the same time.", "forbid6": "Enable at least one of \"Prohibit Copying\" and \"Prohibit Right Clicking\""}, "changeDomainType": "\"ICDN - Upload Acceleration\" and \"ICDN-websocket\" are only available in the Chinese mainland. To change the domain name to the preceding two types, please change the accelerated region to \"Chinese Mainland\" first.", "websocketTip": "The websocket feature is disabled.", "info": "Message", "httpsSupport": {"tip1": "HTTPS service is not enabled. Please {0} first.", "tip2": "subscrib", "tip3": "The domain's product type hasn't enabled HTTPS."}, "entryLimit": {"tips": "When a single IP exceeds the set request limit for a specific domain on a server per second, a 403 status code is returned with a default blocking time of 10 min.", "IP访问限频": "IP rate limiting", "访问阈值": "Access threshold", "IP访问限频规则": "IP rate limit rule", "次/秒": "Count/s", "请输入访问阈值": "Enter the access threshold", "访问阈值取值范围": "Value range: {0} to {1}"}, "您使用的证书存在证书链不完整的情况，存在一定安全隐患，如您仍需提交则点击“继续”按钮进行提交，如您想取消操作则点击“取消”按钮。": "The certificate you are using has an incomplete chain, which poses a security risk. Click \"Continue\" to proceed or \"Cancel\" to abort.", "需配置证书才能开启QUIC功能，默认支持版本：IETF-QUIC(H3-v1)": "QUIC requires the configured certificate. De<PERSON><PERSON> supported version: IETF-QUIC(H3-v1)", "如果域名存在指定源站回源host配置时，则不对此域名更新默认回源host。": "If a domain has a custom origin host configured, its default origin host will not be updated.", "sharedHost": {"title": "Shared <PERSON><PERSON>", "cacheName": "Cache Domain", "selectCacheName": "Please select a cache domain", "tips": "Enabling shared cache will change the cache key. If the shared domain has no cache, it may cause a surge in origin traffic. Please proceed with caution!"}, "remoteAuth": {"远程同步鉴权": "Remote Authentication", "鉴权源站": "Origin", "基础信息": "Basic Info", "鉴权请求uri": "Request URI", "请求配置": "Request", "请求协议": "Protocol", "请求端口": "Port", "请求方法": "Method", "鉴权请求参数": "Auth Parameters", "保留参数设置": "Keep Parameters", "保留所有参数": "Keep all", "删除所有参数": "Remove all", "自定义参数": "Custom Parameters", "自定义": "Custom", "参数名": "Name", "参数值": "Value", "增加参数": "Add Parameter", "参数配置": "Select Parameter", "参数是否区分大小写": "Case sensitive", "参数是否编码": "Encode parameters", "鉴权请求头": "Request Header", "保留请求头设置": "<PERSON> Header", "保留所有请求头": "Keep all headers", "删除所有请求头": "Remove all headers", "自定义请求头": "Custom Request Header", "请求头名称": "Header Name", "请求头值": "Header Value", "增加请求头": "<PERSON>d <PERSON>", "基于状态码鉴权": "Status Code Authentication", "鉴权成功状态码": "Success Status Code", "状态码": "Status Code", "基于响应body鉴权": "Response Body Authentication", "响应body": "Response Body", "鉴权失败时响应状态码": "Authentication Failure Response Status Code", "固定状态码": "Fixed Status Code", "跟随鉴权源站": "Follow Origin", "鉴权超时设置": "Authentication Timeout Settings", "鉴权超时后动作": "Action After Timeout", "通过": "Allow", "拒绝": "<PERSON><PERSON>", "添加鉴权设置": "Add Authentication Configuration", "ip/域名，多个逗号分隔": "IP/Domain separated by commas", "请输入参数名": "Enter name", "请输入参数值": "Enter value", "请输入请求头名称": "Enter header name", "请输入请求头值": "Enter header value", "请输入鉴权源站": "Please enter the auth origin", "格式:/auth 或 $uri": "Format: /auth or $uri", "请输入鉴权请求uri": "Please enter the auth request URI", "选择参数": "Select Parameter", "鉴权状态码类型": "Code Type", "鉴权失败状态码": "Failure Status Code", "鉴权服务器根据鉴权结果返回给CDN的HTTP状态码。例如：配置鉴权成功状态码：200，则代表鉴权服务器返回200时鉴权通过，其他状态码均不通过；配置鉴权失败状态码：403，则代表鉴权服务器返回403时鉴权不通过，其他状态码均通过。": "The auth server returns an HTTP status code to the CDN based on the result. For example, if 200 is set as success, only 200 passes; others fail. If 403 is set as failure, 403 fails; others pass.", "响应状态码类型": "Status Code Type", "添加鉴权配置": "Add Authentication Configuration", "秒": "s", "鉴权超时时间": "Timeout Duration", "状态码鉴权通过时，如果有开启“基于响应body鉴权”，还会结合响应body的内容最终判断是否鉴权通过。": "If status code authentication passes and \"response body-based authentication\" is enabled, the final result will also consider the response body content.", "输入响应body内容": "Enter response body content", "选择请求头": "Select Header", "有值代表添加/修改，值为空代表删除。": "Enter a value to add or modify the parameter, or leave it empty to delete.", "请选择保留参数设置": "Please select a parameter retention setting", "请输入正确的状态码": "Please enter a valid status code", "请选择响应状态码类型": "Please select a response status code type", "请输入响应body内容": "Please enter the response body content", "请选择鉴权状态码类型": "Please select an auth status code type", "请输入正确的超时时间": "Please enter a valid timeout value", "鉴权超时时间取值范围为：0 到 3600": "Auth timeout must be between 0 and 3600", "小数位数最大3位": "Up to 3 decimal places allowed", "不能包含空值": "Cannot contain empty values", "默认3": "Default: 3", "默认403": "Default: 403", "默认80": "Default: 80", "请选择或输入参数名": "Select or enter a parameter name", "请选择或输入参数值": "Select or enter a parameter value", "请选择或输入请求头名称": "Select or enter a header name", "请选择或输入请求头值": "Select or enter a header value", "默认http": "Default: http", "格式错误": "Invalid format", "不能包含空字符串": "Cannot contain empty strings", "默认GET": "Default: GET", "默认443": "Default: 443", "请求体": "Request Body", "默认是": "Yes by default", "多个状态码用逗号分隔": "Separate multiple status codes with commas"}, "请选择指定源站回源HOST": "Please select a specify origin host", "默认回源HOST": "Default Origin Host", "请选择默认回源HOST": "Please select a default origin host", "请输入默认回源HOST": "Enter the default origin host", "源站域名": "Origin Domain", "自定义域名": "Custom Domain", "TLS版本至少包含国际TLS协议中的一种且至少包含国密TLS协议中的一种": "At least one international TLS version and one SM2 TLS version must be included.", "加密套件至少包含国际加密套件中的一种且至少包含国密加密套件中的一种": "At least one international Encryption suite and one SM2 Encryption suite must be included."}