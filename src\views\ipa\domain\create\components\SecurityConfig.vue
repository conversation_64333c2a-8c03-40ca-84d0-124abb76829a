<template>
  <div class="security-config">
    <ct-box class="ct-box-domain-create-wrapper">
      <div class="placeholder">
        <ct-svg-icon icon-class="info-circle" class-name="alert-icon" />
        安全配置暂未开放具体配置项，可直接提交，后续在域名详情中继续配置。
      </div>
    </ct-box>
  </div>
</template>

<script>
import ctSvgIcon from "@/components/ctSvgIcon";

export default {
  name: "SecurityConfig",
  components: { ctSvgIcon },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    // 预留校验方法
    async validateForm() {
      return { valid: true, data: this.formData };
    },
    getFormData() {
      return this.formData;
    }
  }
};
</script>

<style lang="scss" scoped>
.security-config {
  .placeholder {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #606266;
    font-size: 14px;
    padding: 12px 0;
  }
}
</style>